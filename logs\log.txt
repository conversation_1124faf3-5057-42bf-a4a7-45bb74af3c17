=====================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2112 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-DeW5lGXA.css     37.61 kB │ gzip:   6.53 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DumyM77F.js   1,290.50 kB │ gzip: 354.33 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 14.36s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
[Trellis Server] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:44:48"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:44:48"}
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-09 22:44:50"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-09 22:44:52"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-09 22:44:53"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-09 22:44:53"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:44:53"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-09 22:44:54"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-09 22:44:54"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-09 22:44:54"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-09 22:44:57"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-09 22:44:57"}
info: Starting streaming image generation with model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:45:23"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt an old barn interior scene --output N:\3D AI Studio\output\generated_1752119123707_755fac5f-e783-471c-adf8-2dcae85bbb7b.png --model stable-diffusion-xl-base-1.0 --width 1664 --height 1024 --guidance_scale 7.5 --seed 719326656 --refiner_steps 10 --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 22:45:23"}
info: ImageGeneration stderr: WARNING[XFORMERS]: Need to compile C++ extensions to use all xFormers features.
    Please install xformers properly (see https://github.com/facebookresearch/xformers#installing-xformers)
  Memory-efficient attention, SwiGLU, sparse and more won't be available.
  Set XFORMERS_MORE_DETAILS=1 for more details {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stdout: Initializing stable-diffusion-xl-base-1.0 model... {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stdout: Loading model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:45:41"}
info: ImageGeneration stderr: Loading pipeline components...:  14%|#4        | 1/7 [00:05<00:35,  5.84s/it] {"service":"user-service","timestamp":"2025-07-09 22:45:47"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:06<00:12,  2.57s/it] {"service":"user-service","timestamp":"2025-07-09 22:45:47"}
info: ImageGeneration stderr: Loading pipeline components...:  43%|####2     | 3/7 [00:06<00:06,  1.64s/it] {"service":"user-service","timestamp":"2025-07-09 22:45:48"}
info: ImageGeneration stderr: Loading pipeline components...:  71%|#######1  | 5/7 [00:07<00:01,  1.06it/s] {"service":"user-service","timestamp":"2025-07-09 22:45:49"}
Loading pipeline components...: 100%|##########| 7/7 [00:09<00:00,  1.43s/it] {"service":"user-service","timestamp":"2025-07-09 22:45:51"}
info: ImageGeneration stdout: Successfully loaded stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:46:01"}
info: ImageGeneration stdout: Starting image generation... {"service":"user-service","timestamp":"2025-07-09 22:46:01"}
info: ImageGeneration stdout: Generating image with prompt: 'an old barn interior scene' {"service":"user-service","timestamp":"2025-07-09 22:46:01"}
info: ImageGeneration stdout: Parameters: 1664x1024, steps=20, guidance=7.5, seed=719326656 {"service":"user-service","timestamp":"2025-07-09 22:46:01"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-09 22:46:01"}
info: ImageGeneration stderr: 0%|          | 0/20 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:46:02"}
info: ImageGeneration stderr: 5%|5         | 1/20 [00:05<01:53,  5.96s/it] {"service":"user-service","timestamp":"2025-07-09 22:46:08"}
info: ImageGeneration stdout: Latents shape: torch.Size([1, 4, 128, 208]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: ImageGeneration stdout: Latents min: -37.5625, max: 36.5625 {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: ImageGeneration stdout: Decoded tensor shape: torch.Size([1, 3, 1024, 1664]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: ImageGeneration stdout: Decoded tensor min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: ImageGeneration stdout: After normalization min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: ImageGeneration stdout: Image array shape: (1024, 1664, 3), min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: ImageGeneration stdout: Final array min: 0, max: 0 {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: Received preview image, base64 length: 4252 {"service":"user-service","timestamp":"2025-07-09 22:46:14"}
info: ImageGeneration stderr: 10%|#         | 2/20 [00:15<02:27,  8.17s/it] {"service":"user-service","timestamp":"2025-07-09 22:46:18"}
info: ImageGeneration stdout: Latents shape: torch.Size([1, 4, 128, 208]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: ImageGeneration stdout: Latents min: -28.09375, max: 30.46875 {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: ImageGeneration stdout: Decoded tensor shape: torch.Size([1, 3, 1024, 1664]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: ImageGeneration stdout: Decoded tensor min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: ImageGeneration stdout: After normalization min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: ImageGeneration stdout: Image array shape: (1024, 1664, 3), min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: ImageGeneration stdout: Final array min: 0, max: 0 {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: Received preview image, base64 length: 4252 {"service":"user-service","timestamp":"2025-07-09 22:46:24"}
info: ImageGeneration stderr: 15%|#5        | 3/20 [00:26<02:35,  9.17s/it] {"service":"user-service","timestamp":"2025-07-09 22:46:28"}
info: ImageGeneration stdout: Latents shape: torch.Size([1, 4, 128, 208]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: ImageGeneration stdout: Latents min: -24.09375, max: 21.84375 {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: ImageGeneration stdout: Decoded tensor shape: torch.Size([1, 3, 1024, 1664]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: ImageGeneration stdout: Decoded tensor min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: ImageGeneration stdout: After normalization min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: ImageGeneration stdout: Image array shape: (1024, 1664, 3), min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: ImageGeneration stdout: Final array min: 0, max: 0 {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: Received preview image, base64 length: 4252 {"service":"user-service","timestamp":"2025-07-09 22:46:35"}
info: ImageGeneration stderr: 20%|##        | 4/20 [00:36<02:33,  9.58s/it] {"service":"user-service","timestamp":"2025-07-09 22:46:39"}
info: ImageGeneration stdout: Latents shape: torch.Size([1, 4, 128, 208]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: ImageGeneration stdout: Latents min: -18.875, max: 16.796875 {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: ImageGeneration stdout: Decoded tensor shape: torch.Size([1, 3, 1024, 1664]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: ImageGeneration stdout: Decoded tensor min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: ImageGeneration stdout: After normalization min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: ImageGeneration stdout: Image array shape: (1024, 1664, 3), min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: ImageGeneration stdout: Final array min: 0, max: 0 {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: Received preview image, base64 length: 4252 {"service":"user-service","timestamp":"2025-07-09 22:46:45"}
info: ImageGeneration stderr: 25%|##5       | 5/20 [00:46<02:28,  9.88s/it] {"service":"user-service","timestamp":"2025-07-09 22:46:49"}
info: ImageGeneration stdout: Latents shape: torch.Size([1, 4, 128, 208]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: ImageGeneration stdout: Latents min: -16.0625, max: 14.015625 {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: ImageGeneration stdout: Decoded tensor shape: torch.Size([1, 3, 1024, 1664]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: ImageGeneration stdout: Decoded tensor min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: ImageGeneration stdout: After normalization min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: ImageGeneration stdout: Image array shape: (1024, 1664, 3), min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: ImageGeneration stdout: Final array min: 0, max: 0 {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: Received preview image, base64 length: 4252 {"service":"user-service","timestamp":"2025-07-09 22:46:55"}
info: ImageGeneration stderr: 30%|###       | 6/20 [00:56<02:19, 10.00s/it] {"service":"user-service","timestamp":"2025-07-09 22:46:59"}
info: ImageGeneration stdout: Latents shape: torch.Size([1, 4, 128, 208]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:47:05"}
info: ImageGeneration stdout: Latents min: -13.453125, max: 11.4140625 {"service":"user-service","timestamp":"2025-07-09 22:47:05"}
info: ImageGeneration stdout: Decoded tensor shape: torch.Size([1, 3, 1024, 1664]), dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:47:05"}
info: ImageGeneration stdout: Decoded tensor min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:47:05"}
info: ImageGeneration stdout: After normalization min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:47:05"}
info: ImageGeneration stdout: Image array shape: (1024, 1664, 3), min: nan, max: nan {"service":"user-service","timestamp":"2025-07-09 22:47:05"}
info: ImageGeneration stdout: Final array min: 0, max: 0 {"service":"user-service","timestamp":"2025-07-09 22:47:05"}
info: Received preview image, base64 length: 4252 {"service":"user-service","timestamp":"2025-07-09 22:47:05"}