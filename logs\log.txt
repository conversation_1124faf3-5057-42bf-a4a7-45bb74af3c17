========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2112 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-D2XR8lv2.css     37.63 kB │ gzip:   6.53 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DtnVvXgw.js   1,290.86 kB │ gzip: 354.43 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.07s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
[Trellis Server] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:07:43"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:07:43"}
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-09 22:07:45"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-09 22:07:46"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:07:47"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-09 22:07:48"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-09 22:07:48"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-09 22:07:48"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-09 22:07:51"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-09 22:07:51"}
info: Starting streaming image generation with model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:08:30"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt an old barn interior scene --output N:\3D AI Studio\output\generated_1752116910586_e5bbd4f0-4d9c-46cd-8a22-2b153ee45b6e.png --model stable-diffusion-xl-base-1.0 --width 1600 --height 1024 --guidance_scale 7.5 --seed 2007813892 --use_refiner --refiner_steps 10 --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 22:08:30"}
info: ImageGeneration stderr: WARNING[XFORMERS]: Need to compile C++ extensions to use all xFormers features.
    Please install xformers properly (see https://github.com/facebookresearch/xformers#installing-xformers)
  Memory-efficient attention, SwiGLU, sparse and more won't be available.
  Set XFORMERS_MORE_DETAILS=1 for more details {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stdout: Initializing stable-diffusion-xl-base-1.0 model... {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stdout: Loading model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:08:46"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:02<00:06,  1.32s/it] {"service":"user-service","timestamp":"2025-07-09 22:08:49"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [00:03<00:02,  1.41it/s] {"service":"user-service","timestamp":"2025-07-09 22:08:50"}
info: ImageGeneration stderr: Loading pipeline components...:  86%|########5 | 6/7 [00:04<00:00,  1.76it/s] {"service":"user-service","timestamp":"2025-07-09 22:08:50"}
Loading pipeline components...: 100%|##########| 7/7 [00:10<00:00,  1.45s/it] {"service":"user-service","timestamp":"2025-07-09 22:08:57"}
info: ImageGeneration stdout: Successfully loaded stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:09:06"}
info: ImageGeneration stdout: Starting image generation... {"service":"user-service","timestamp":"2025-07-09 22:09:06"}
info: ImageGeneration stdout: Generating image with prompt: 'an old barn interior scene' {"service":"user-service","timestamp":"2025-07-09 22:09:06"}
info: ImageGeneration stdout: Parameters: 1600x1024, steps=20, guidance=7.5, seed=2007813892 {"service":"user-service","timestamp":"2025-07-09 22:09:06"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-09 22:09:06"}
info: ImageGeneration stderr: 0%|          | 0/20 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:09:08"}
info: ImageGeneration stderr: 5%|5         | 1/20 [00:05<01:47,  5.64s/it] {"service":"user-service","timestamp":"2025-07-09 22:09:14"}
info: ImageGeneration stderr: N:\3D AI Studio\utils\helpers\generate_image.py:153: RuntimeWarning: invalid value encountered in cast
  image_array = (image_tensor[0] * 255).astype(np.uint8) {"service":"user-service","timestamp":"2025-07-09 22:09:18"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:09:19"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:19"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:19"}
info: ImageGeneration stderr: 10%|#         | 2/20 [00:14<02:19,  7.77s/it] {"service":"user-service","timestamp":"2025-07-09 22:09:23"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:09:28"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:28"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:28"}
info: ImageGeneration stderr: 15%|#5        | 3/20 [00:24<02:24,  8.53s/it] {"service":"user-service","timestamp":"2025-07-09 22:09:32"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:09:38"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:38"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:38"}
info: ImageGeneration stderr: 20%|##        | 4/20 [00:33<02:22,  8.92s/it] {"service":"user-service","timestamp":"2025-07-09 22:09:42"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:09:47"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:47"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:47"}
info: ImageGeneration stderr: 25%|##5       | 5/20 [00:43<02:16,  9.08s/it] {"service":"user-service","timestamp":"2025-07-09 22:09:51"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:09:56"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:56"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:09:56"}
info: ImageGeneration stderr: 30%|###       | 6/20 [00:52<02:08,  9.19s/it] {"service":"user-service","timestamp":"2025-07-09 22:10:01"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:10:06"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:10:06"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:10:06"}
info: ImageGeneration stderr: 35%|###5      | 7/20 [01:02<02:01,  9.38s/it] {"service":"user-service","timestamp":"2025-07-09 22:10:10"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:10:16"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:10:16"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:10:16"}
info: ImageGeneration stderr: 40%|####      | 8/20 [01:12<01:54,  9.53s/it] {"service":"user-service","timestamp":"2025-07-09 22:10:20"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:10:26"}
info: ImageGeneration stdout: Generated preview image, base64 length: 43452 {"service":"user-service","timestamp":"2025-07-09 22:10:26"}
[PipelineManager] Forwarding preview image, base64 length: 43452
info: Received preview image, base64 length: 43452 {"service":"user-service","timestamp":"2025-07-09 22:10:26"}
info: ImageGeneration stderr: 45%|####5     | 9/20 [01:22<01:45,  9.62s/it] {"service":"user-service","timestamp":"2025-07-09 22:10:30"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:10:35"}
info: ImageGeneration stdout: Generated preview image, base64 length: 42948 {"service":"user-service","timestamp":"2025-07-09 22:10:35"}
[PipelineManager] Forwarding preview image, base64 length: 42948
info: Received preview image, base64 length: 42948 {"service":"user-service","timestamp":"2025-07-09 22:10:35"}
info: ImageGeneration stderr: 50%|#####     | 10/20 [01:31<01:36,  9.60s/it] {"service":"user-service","timestamp":"2025-07-09 22:10:40"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:10:45"}
info: ImageGeneration stdout: Generated preview image, base64 length: 42636 {"service":"user-service","timestamp":"2025-07-09 22:10:45"}
[PipelineManager] Forwarding preview image, base64 length: 42636
info: Received preview image, base64 length: 42636 {"service":"user-service","timestamp":"2025-07-09 22:10:45"}
info: ImageGeneration stderr: 55%|#####5    | 11/20 [01:41<01:27,  9.67s/it] {"service":"user-service","timestamp":"2025-07-09 22:10:49"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:10:55"}
info: ImageGeneration stdout: Generated preview image, base64 length: 42248 {"service":"user-service","timestamp":"2025-07-09 22:10:55"}
[PipelineManager] Forwarding preview image, base64 length: 42248
info: Received preview image, base64 length: 42248 {"service":"user-service","timestamp":"2025-07-09 22:10:55"}
info: ImageGeneration stderr: 60%|######    | 12/20 [01:50<01:16,  9.62s/it] {"service":"user-service","timestamp":"2025-07-09 22:10:59"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:11:04"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:04"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:04"}
info: ImageGeneration stderr: 65%|######5   | 13/20 [02:00<01:06,  9.52s/it] {"service":"user-service","timestamp":"2025-07-09 22:11:08"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:11:14"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:14"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:14"}
info: ImageGeneration stderr: 70%|#######   | 14/20 [02:09<00:57,  9.58s/it] {"service":"user-service","timestamp":"2025-07-09 22:11:18"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:11:23"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:23"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:23"}
info: ImageGeneration stderr: 75%|#######5  | 15/20 [02:19<00:47,  9.58s/it] {"service":"user-service","timestamp":"2025-07-09 22:11:27"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:11:33"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:33"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:33"}
info: ImageGeneration stderr: 80%|########  | 16/20 [02:28<00:38,  9.54s/it] {"service":"user-service","timestamp":"2025-07-09 22:11:37"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:11:42"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:42"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:42"}
info: ImageGeneration stderr: 85%|########5 | 17/20 [02:38<00:28,  9.50s/it] {"service":"user-service","timestamp":"2025-07-09 22:11:46"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:11:52"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:52"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:11:52"}
info: ImageGeneration stderr: 90%|######### | 18/20 [02:47<00:18,  9.46s/it] {"service":"user-service","timestamp":"2025-07-09 22:11:56"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:12:01"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:12:01"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:12:01"}
info: ImageGeneration stderr: 95%|#########5| 19/20 [02:57<00:09,  9.49s/it] {"service":"user-service","timestamp":"2025-07-09 22:12:05"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:12:11"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:12:11"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:12:11"}
info: ImageGeneration stderr: 100%|##########| 20/20 [03:06<00:00,  9.50s/it] {"service":"user-service","timestamp":"2025-07-09 22:12:15"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:12:20"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:12:20"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:12:20"}
info: ImageGeneration stderr: 100%|##########| 20/20 [03:11<00:00,  9.60s/it] {"service":"user-service","timestamp":"2025-07-09 22:12:20"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/5 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:12:34"}
info: ImageGeneration stderr: Loading pipeline components...:  20%|##        | 1/5 [00:04<00:16,  4.09s/it] {"service":"user-service","timestamp":"2025-07-09 22:12:38"}
info: ImageGeneration stderr: Loading pipeline components...:  60%|######    | 3/5 [00:04<00:02,  1.32s/it] {"service":"user-service","timestamp":"2025-07-09 22:12:39"}
info: ImageGeneration stderr: Loading pipeline components...:  80%|########  | 4/5 [00:04<00:00,  1.10it/s] {"service":"user-service","timestamp":"2025-07-09 22:12:39"}
Loading pipeline components...: 100%|##########| 5/5 [00:15<00:00,  3.12s/it] {"service":"user-service","timestamp":"2025-07-09 22:12:49"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl_img2img.py:1199: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl_img2img.py:1205: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-09 22:12:54"}
info: ImageGeneration stderr: 0%|          | 0/10 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:12:56"}
info: ImageGeneration stderr: 10%|#         | 1/10 [00:04<00:37,  4.19s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:01"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:05"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:05"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:05"}
info: ImageGeneration stderr: 20%|##        | 2/10 [00:09<00:37,  4.74s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:06"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:09"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:09"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:09"}
info: ImageGeneration stderr: 30%|###       | 3/10 [00:13<00:30,  4.30s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:09"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:12"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:12"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:12"}
info: ImageGeneration stderr: 40%|####      | 4/10 [00:16<00:24,  4.08s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:13"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:16"}
info: ImageGeneration stdout: Generated preview image, base64 length: 41860 {"service":"user-service","timestamp":"2025-07-09 22:13:16"}
[PipelineManager] Forwarding preview image, base64 length: 41860
info: Received preview image, base64 length: 41860 {"service":"user-service","timestamp":"2025-07-09 22:13:16"}
info: ImageGeneration stderr: 50%|#####     | 5/10 [00:20<00:19,  3.96s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:17"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:20"}
info: ImageGeneration stdout: Generated preview image, base64 length: 40364 {"service":"user-service","timestamp":"2025-07-09 22:13:20"}
[PipelineManager] Forwarding preview image, base64 length: 40364
info: Received preview image, base64 length: 40364 {"service":"user-service","timestamp":"2025-07-09 22:13:20"}
info: ImageGeneration stderr: 60%|######    | 6/10 [00:24<00:15,  3.88s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:21"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:24"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:24"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:24"}
info: ImageGeneration stderr: 70%|#######   | 7/10 [00:28<00:11,  3.89s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:25"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:28"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:28"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:28"}
info: ImageGeneration stderr: 80%|########  | 8/10 [00:32<00:07,  3.86s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:28"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:31"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:31"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:31"}
info: ImageGeneration stderr: 90%|######### | 9/10 [00:35<00:03,  3.83s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:32"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:35"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:35"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:35"}
info: ImageGeneration stderr: 100%|##########| 10/10 [00:39<00:00,  3.85s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:36"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:13:39"}
info: ImageGeneration stdout: Generated preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:39"}
[PipelineManager] Forwarding preview image, base64 length: 1776
info: Received preview image, base64 length: 1776 {"service":"user-service","timestamp":"2025-07-09 22:13:39"}
info: ImageGeneration stderr: 100%|##########| 10/10 [00:42<00:00,  4.27s/it] {"service":"user-service","timestamp":"2025-07-09 22:13:39"}
info: ImageGeneration stdout: Successfully generated image: N:\3D AI Studio\output\generated_1752116910586_e5bbd4f0-4d9c-46cd-8a22-2b153ee45b6e.png {"service":"user-service","timestamp":"2025-07-09 22:14:03"}
info: ImageGeneration stdout: {"success": true, "output_path": "N:\\3D AI Studio\\output\\generated_1752116910586_e5bbd4f0-4d9c-46cd-8a22-2b153ee45b6e.png", "model": "stable-diffusion-xl-base-1.0", "prompt": "an old barn interior scene", "width": 1600, "height": 1024, "steps": 20, "guidance_scale": 7.5, "seed": 2007813892, "execution_time": 317.0860598087311} {"service":"user-service","timestamp":"2025-07-09 22:14:03"}
info: Image generation process exited with code: 0 {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Reading result file: pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Image generation completed successfully: N:\3D AI Studio\output\generated_1752116910586_e5bbd4f0-4d9c-46cd-8a22-2b153ee45b6e.png {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Result details: success=true, model=stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Pipeline result received: success=true, output_path=N:\3D AI Studio\output\generated_1752116910586_e5bbd4f0-4d9c-46cd-8a22-2b153ee45b6e.png {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Processing successful result... {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Checking if image file exists: N:\3D AI Studio\output\generated_1752116910586_e5bbd4f0-4d9c-46cd-8a22-2b153ee45b6e.png {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Reading image file as base64... {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Image converted to base64 successfully (3192292 chars) {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Sending final result to frontend... {"service":"user-service","timestamp":"2025-07-09 22:14:09"}
info: Final result sent to frontend successfully {"service":"user-service","timestamp":"2025-07-09 22:14:09"}