========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2112 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-DeW5lGXA.css     37.61 kB │ gzip:   6.53 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DumyM77F.js   1,290.50 kB │ gzip: 354.33 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.07s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
[Trellis Server] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 23:13:00"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 23:13:00"}
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-09 23:13:02"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-09 23:13:04"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 23:13:05"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-09 23:13:06"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-09 23:13:06"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-09 23:13:06"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-09 23:13:09"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-09 23:13:09"}
info: Starting streaming image generation with model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:19:41"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt an old barn scene --output N:\3D AI Studio\output\generated_1752121181087_fb20d693-9f71-41bd-b917-4a5e2890a089.png --model stable-diffusion-xl-base-1.0 --width 1600 --height 1024 --guidance_scale 7.5 --seed 411842634 --refiner_steps 10 --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 23:19:41"}
info: ImageGeneration stderr: WARNING[XFORMERS]: Need to compile C++ extensions to use all xFormers features.
    Please install xformers properly (see https://github.com/facebookresearch/xformers#installing-xformers)
  Memory-efficient attention, SwiGLU, sparse and more won't be available.
  Set XFORMERS_MORE_DETAILS=1 for more details {"service":"user-service","timestamp":"2025-07-09 23:19:56"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-09 23:19:56"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stdout: Initializing stable-diffusion-xl-base-1.0 model... {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stdout: Loading model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 23:19:57"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:02<00:06,  1.21s/it] {"service":"user-service","timestamp":"2025-07-09 23:19:59"}
info: ImageGeneration stderr: Loading pipeline components...:  43%|####2     | 3/7 [00:02<00:02,  1.34it/s] {"service":"user-service","timestamp":"2025-07-09 23:19:59"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [00:07<00:07,  2.50s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:04"}
info: ImageGeneration stderr: Loading pipeline components...:  86%|########5 | 6/7 [00:08<00:01,  1.35s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:05"}
Loading pipeline components...: 100%|##########| 7/7 [00:09<00:00,  1.31s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:06"}
info: ImageGeneration stdout: Successfully loaded stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:20:15"}
info: ImageGeneration stdout: Starting image generation... {"service":"user-service","timestamp":"2025-07-09 23:20:15"}
info: ImageGeneration stdout: Generating image with prompt: 'an old barn scene' {"service":"user-service","timestamp":"2025-07-09 23:20:15"}
info: ImageGeneration stdout: Parameters: 1600x1024, steps=20, guidance=7.5, seed=411842634 {"service":"user-service","timestamp":"2025-07-09 23:20:15"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-09 23:20:15"}
info: ImageGeneration stderr: 0%|          | 0/20 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 23:20:17"}
info: ImageGeneration stderr: 5%|5         | 1/20 [00:05<01:44,  5.50s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:22"}
info: Received preview image, base64 length: 108464 {"service":"user-service","timestamp":"2025-07-09 23:20:22"}
info: ImageGeneration stderr: 10%|#         | 2/20 [00:06<00:49,  2.74s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:23"}
info: Received preview image, base64 length: 104684 {"service":"user-service","timestamp":"2025-07-09 23:20:23"}
info: ImageGeneration stderr: 15%|#5        | 3/20 [00:07<00:35,  2.07s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:24"}
info: Received preview image, base64 length: 108968 {"service":"user-service","timestamp":"2025-07-09 23:20:25"}
info: ImageGeneration stderr: 20%|##        | 4/20 [00:08<00:28,  1.76s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:25"}
info: Received preview image, base64 length: 108508 {"service":"user-service","timestamp":"2025-07-09 23:20:26"}
info: ImageGeneration stderr: 25%|##5       | 5/20 [00:10<00:23,  1.58s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:27"}
info: Received preview image, base64 length: 108828 {"service":"user-service","timestamp":"2025-07-09 23:20:27"}
info: ImageGeneration stderr: 30%|###       | 6/20 [00:11<00:20,  1.47s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:28"}
info: Received preview image, base64 length: 110864 {"service":"user-service","timestamp":"2025-07-09 23:20:29"}
info: ImageGeneration stderr: 35%|###5      | 7/20 [00:12<00:18,  1.41s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:29"}
info: Received preview image, base64 length: 110984 {"service":"user-service","timestamp":"2025-07-09 23:20:30"}
info: ImageGeneration stderr: 40%|####      | 8/20 [00:13<00:16,  1.37s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:31"}
info: Received preview image, base64 length: 111276 {"service":"user-service","timestamp":"2025-07-09 23:20:31"}
info: ImageGeneration stderr: 45%|####5     | 9/20 [00:15<00:14,  1.33s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:32"}
info: Received preview image, base64 length: 112128 {"service":"user-service","timestamp":"2025-07-09 23:20:32"}
info: ImageGeneration stderr: 50%|#####     | 10/20 [00:16<00:13,  1.32s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:33"}
info: Received preview image, base64 length: 113304 {"service":"user-service","timestamp":"2025-07-09 23:20:34"}
info: ImageGeneration stderr: 55%|#####5    | 11/20 [00:17<00:11,  1.30s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:34"}
info: Received preview image, base64 length: 112136 {"service":"user-service","timestamp":"2025-07-09 23:20:35"}
info: ImageGeneration stderr: 60%|######    | 12/20 [00:19<00:10,  1.30s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:36"}
info: Received preview image, base64 length: 112348 {"service":"user-service","timestamp":"2025-07-09 23:20:36"}
info: ImageGeneration stderr: 65%|######5   | 13/20 [00:20<00:09,  1.29s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:37"}
info: Received preview image, base64 length: 109892 {"service":"user-service","timestamp":"2025-07-09 23:20:37"}
info: ImageGeneration stderr: 70%|#######   | 14/20 [00:21<00:07,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:38"}
info: Received preview image, base64 length: 108924 {"service":"user-service","timestamp":"2025-07-09 23:20:39"}
info: ImageGeneration stderr: 75%|#######5  | 15/20 [00:22<00:06,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:39"}
info: Received preview image, base64 length: 107080 {"service":"user-service","timestamp":"2025-07-09 23:20:40"}
info: ImageGeneration stderr: 80%|########  | 16/20 [00:24<00:05,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:41"}
info: Received preview image, base64 length: 106100 {"service":"user-service","timestamp":"2025-07-09 23:20:41"}
info: ImageGeneration stderr: 85%|########5 | 17/20 [00:25<00:03,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:42"}
info: Received preview image, base64 length: 103960 {"service":"user-service","timestamp":"2025-07-09 23:20:43"}
info: ImageGeneration stderr: 90%|######### | 18/20 [00:26<00:02,  1.27s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:43"}
info: Received preview image, base64 length: 101300 {"service":"user-service","timestamp":"2025-07-09 23:20:44"}
info: ImageGeneration stderr: 95%|#########5| 19/20 [00:27<00:01,  1.27s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:45"}
info: Received preview image, base64 length: 93852 {"service":"user-service","timestamp":"2025-07-09 23:20:45"}
info: ImageGeneration stderr: 100%|##########| 20/20 [00:29<00:00,  1.27s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:46"}
info: Received preview image, base64 length: 92432 {"service":"user-service","timestamp":"2025-07-09 23:20:46"}
info: ImageGeneration stderr: 100%|##########| 20/20 [00:29<00:00,  1.49s/it] {"service":"user-service","timestamp":"2025-07-09 23:20:46"}
info: ImageGeneration stdout: Successfully generated image: N:\3D AI Studio\output\generated_1752121181087_fb20d693-9f71-41bd-b917-4a5e2890a089.png {"service":"user-service","timestamp":"2025-07-09 23:20:57"}
info: ImageGeneration stdout: {"success": true, "output_path": "N:\\3D AI Studio\\output\\generated_1752121181087_fb20d693-9f71-41bd-b917-4a5e2890a089.png", "model": "stable-diffusion-xl-base-1.0", "prompt": "an old barn scene", "width": 1600, "height": 1024, "steps": 20, "guidance_scale": 7.5, "seed": 411842634, "execution_time": 60.88172483444214} {"service":"user-service","timestamp":"2025-07-09 23:20:57"}
info: Image generation process exited with code: 0 {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Reading result file: pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Image generation completed successfully: N:\3D AI Studio\output\generated_1752121181087_fb20d693-9f71-41bd-b917-4a5e2890a089.png {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Result details: success=true, model=stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Pipeline result received: success=true, output_path=N:\3D AI Studio\output\generated_1752121181087_fb20d693-9f71-41bd-b917-4a5e2890a089.png {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Processing successful result... {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Checking if image file exists: N:\3D AI Studio\output\generated_1752121181087_fb20d693-9f71-41bd-b917-4a5e2890a089.png {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Reading image file as base64... {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Image converted to base64 successfully (4304032 chars) {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Sending final result to frontend... {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Final result sent to frontend successfully {"service":"user-service","timestamp":"2025-07-09 23:21:01"}
info: Starting streaming image generation with model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:25:15"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt an old barn scene --output N:\3D AI Studio\output\generated_1752121515454_7496b7f8-bba2-4fc2-a050-858fcf8e314e.png --model stable-diffusion-xl-base-1.0 --width 1600 --height 1024 --guidance_scale 7.5 --seed 1497676019 --refiner_steps 10 --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 23:25:15"}
info: ImageGeneration stderr: WARNING[XFORMERS]: Need to compile C++ extensions to use all xFormers features.
    Please install xformers properly (see https://github.com/facebookresearch/xformers#installing-xformers)
  Memory-efficient attention, SwiGLU, sparse and more won't be available.
  Set XFORMERS_MORE_DETAILS=1 for more details {"service":"user-service","timestamp":"2025-07-09 23:25:30"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-09 23:25:30"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stdout: Initializing stable-diffusion-xl-base-1.0 model... {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stdout: Loading model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:00<00:00, 17.24it/s] {"service":"user-service","timestamp":"2025-07-09 23:25:31"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [00:06<00:05,  1.94s/it] {"service":"user-service","timestamp":"2025-07-09 23:25:38"}
info: ImageGeneration stderr: Loading pipeline components...:  71%|#######1  | 5/7 [00:08<00:03,  1.96s/it] {"service":"user-service","timestamp":"2025-07-09 23:25:40"}
Loading pipeline components...: 100%|##########| 7/7 [00:09<00:00,  1.35s/it] {"service":"user-service","timestamp":"2025-07-09 23:25:40"}
info: ImageGeneration stdout: Successfully loaded stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:25:49"}
info: ImageGeneration stdout: Starting image generation... {"service":"user-service","timestamp":"2025-07-09 23:25:49"}
info: ImageGeneration stdout: Generating image with prompt: 'an old barn scene' {"service":"user-service","timestamp":"2025-07-09 23:25:49"}
info: ImageGeneration stdout: Parameters: 1600x1024, steps=20, guidance=7.5, seed=1497676019 {"service":"user-service","timestamp":"2025-07-09 23:25:49"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-09 23:25:49"}
info: ImageGeneration stderr: 0%|          | 0/20 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 23:25:51"}
info: ImageGeneration stderr: 5%|5         | 1/20 [00:05<01:44,  5.51s/it] {"service":"user-service","timestamp":"2025-07-09 23:25:56"}
info: Received preview image, base64 length: 110272 {"service":"user-service","timestamp":"2025-07-09 23:25:56"}
info: ImageGeneration stderr: 10%|#         | 2/20 [00:06<00:49,  2.77s/it] {"service":"user-service","timestamp":"2025-07-09 23:25:57"}
info: Received preview image, base64 length: 109456 {"service":"user-service","timestamp":"2025-07-09 23:25:58"}
info: ImageGeneration stderr: 15%|#5        | 3/20 [00:07<00:35,  2.10s/it] {"service":"user-service","timestamp":"2025-07-09 23:25:58"}
info: Received preview image, base64 length: 107732 {"service":"user-service","timestamp":"2025-07-09 23:25:59"}
info: ImageGeneration stderr: 20%|##        | 4/20 [00:08<00:28,  1.77s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:00"}
info: Received preview image, base64 length: 112004 {"service":"user-service","timestamp":"2025-07-09 23:26:00"}
info: ImageGeneration stderr: 25%|##5       | 5/20 [00:10<00:23,  1.60s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:01"}
info: Received preview image, base64 length: 108292 {"service":"user-service","timestamp":"2025-07-09 23:26:02"}
info: ImageGeneration stderr: 30%|###       | 6/20 [00:11<00:20,  1.49s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:02"}
info: Received preview image, base64 length: 108552 {"service":"user-service","timestamp":"2025-07-09 23:26:03"}
info: ImageGeneration stderr: 35%|###5      | 7/20 [00:12<00:18,  1.42s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:04"}
info: Received preview image, base64 length: 110688 {"service":"user-service","timestamp":"2025-07-09 23:26:04"}
info: ImageGeneration stderr: 40%|####      | 8/20 [00:14<00:16,  1.37s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:05"}
info: Received preview image, base64 length: 111796 {"service":"user-service","timestamp":"2025-07-09 23:26:05"}
info: ImageGeneration stderr: 45%|####5     | 9/20 [00:15<00:14,  1.34s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:06"}
info: Received preview image, base64 length: 108432 {"service":"user-service","timestamp":"2025-07-09 23:26:07"}
info: ImageGeneration stderr: 50%|#####     | 10/20 [00:16<00:13,  1.32s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:07"}
info: Received preview image, base64 length: 113356 {"service":"user-service","timestamp":"2025-07-09 23:26:08"}
info: ImageGeneration stderr: 55%|#####5    | 11/20 [00:17<00:11,  1.31s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:09"}
info: Received preview image, base64 length: 106956 {"service":"user-service","timestamp":"2025-07-09 23:26:09"}
info: ImageGeneration stderr: 60%|######    | 12/20 [00:19<00:10,  1.30s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:10"}
info: Received preview image, base64 length: 111364 {"service":"user-service","timestamp":"2025-07-09 23:26:10"}
info: ImageGeneration stderr: 65%|######5   | 13/20 [00:20<00:09,  1.29s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:11"}
info: Received preview image, base64 length: 109200 {"service":"user-service","timestamp":"2025-07-09 23:26:12"}
info: ImageGeneration stderr: 70%|#######   | 14/20 [00:21<00:07,  1.29s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:13"}
info: Received preview image, base64 length: 110756 {"service":"user-service","timestamp":"2025-07-09 23:26:13"}
info: ImageGeneration stderr: 75%|#######5  | 15/20 [00:22<00:06,  1.29s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:14"}
info: Received preview image, base64 length: 107992 {"service":"user-service","timestamp":"2025-07-09 23:26:14"}
info: ImageGeneration stderr: 80%|########  | 16/20 [00:24<00:05,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:15"}
info: Received preview image, base64 length: 108460 {"service":"user-service","timestamp":"2025-07-09 23:26:16"}
info: ImageGeneration stderr: 85%|########5 | 17/20 [00:25<00:03,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:16"}
info: Received preview image, base64 length: 104892 {"service":"user-service","timestamp":"2025-07-09 23:26:17"}
info: ImageGeneration stderr: 90%|######### | 18/20 [00:26<00:02,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:18"}
info: Received preview image, base64 length: 102916 {"service":"user-service","timestamp":"2025-07-09 23:26:18"}
info: ImageGeneration stderr: 95%|#########5| 19/20 [00:28<00:01,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:19"}
info: Received preview image, base64 length: 98688 {"service":"user-service","timestamp":"2025-07-09 23:26:19"}
info: ImageGeneration stderr: 100%|##########| 20/20 [00:29<00:00,  1.28s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:20"}
info: Received preview image, base64 length: 97872 {"service":"user-service","timestamp":"2025-07-09 23:26:21"}
info: ImageGeneration stderr: 100%|##########| 20/20 [00:29<00:00,  1.50s/it] {"service":"user-service","timestamp":"2025-07-09 23:26:21"}
info: ImageGeneration stdout: Successfully generated image: N:\3D AI Studio\output\generated_1752121515454_7496b7f8-bba2-4fc2-a050-858fcf8e314e.png {"service":"user-service","timestamp":"2025-07-09 23:26:43"}
info: ImageGeneration stdout: {"success": true, "output_path": "N:\\3D AI Studio\\output\\generated_1752121515454_7496b7f8-bba2-4fc2-a050-858fcf8e314e.png", "model": "stable-diffusion-xl-base-1.0", "prompt": "an old barn scene", "width": 1600, "height": 1024, "steps": 20, "guidance_scale": 7.5, "seed": 1497676019, "execution_time": 71.62048149108887} {"service":"user-service","timestamp":"2025-07-09 23:26:43"}
info: Image generation process exited with code: 0 {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Reading result file: pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Image generation completed successfully: N:\3D AI Studio\output\generated_1752121515454_7496b7f8-bba2-4fc2-a050-858fcf8e314e.png {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Result details: success=true, model=stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Pipeline result received: success=true, output_path=N:\3D AI Studio\output\generated_1752121515454_7496b7f8-bba2-4fc2-a050-858fcf8e314e.png {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Processing successful result... {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Checking if image file exists: N:\3D AI Studio\output\generated_1752121515454_7496b7f8-bba2-4fc2-a050-858fcf8e314e.png {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Reading image file as base64... {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Image converted to base64 successfully (4272572 chars) {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Sending final result to frontend... {"service":"user-service","timestamp":"2025-07-09 23:26:46"}
info: Final result sent to frontend successfully {"service":"user-service","timestamp":"2025-07-09 23:26:46"}