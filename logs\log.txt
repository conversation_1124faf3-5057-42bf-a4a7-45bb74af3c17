========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2112 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.17 kB
../../dist/renderer/assets/index-DeW5lGXA.css     37.61 kB │ gzip:   6.53 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-g07VA3-2.js   1,290.61 kB │ gzip: 354.36 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.84s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
[Trellis Server] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:25:19"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:25:19"}
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-09 22:25:21"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-09 22:25:23"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 22:25:24"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-09 22:25:25"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-09 22:25:25"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-09 22:25:25"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-09 22:25:28"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-09 22:25:28"}
info: Starting streaming image generation with model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:31:58"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt an old barn interior scene --output N:\3D AI Studio\output\generated_1752118318364_568703a8-cca1-4de5-9917-461147fb77cc.png --model stable-diffusion-xl-base-1.0 --width 1600 --height 1024 --guidance_scale 7.5 --seed 179611904 --refiner_steps 10 --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-09 22:31:58"}
info: ImageGeneration stderr: WARNING[XFORMERS]: Need to compile C++ extensions to use all xFormers features.
    Please install xformers properly (see https://github.com/facebookresearch/xformers#installing-xformers)
  Memory-efficient attention, SwiGLU, sparse and more won't be available.
  Set XFORMERS_MORE_DETAILS=1 for more details {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stdout: Initializing stable-diffusion-xl-base-1.0 model... {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stdout: Loading model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:32:14"}
info: ImageGeneration stderr: Loading pipeline components...:  14%|#4        | 1/7 [00:02<00:16,  2.75s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:17"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:09<00:24,  4.91s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:23"}
info: ImageGeneration stderr: Loading pipeline components...:  43%|####2     | 3/7 [00:09<00:11,  2.98s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:24"}
info: ImageGeneration stderr: Loading pipeline components...:  71%|#######1  | 5/7 [00:09<00:02,  1.32s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:24"}
info: ImageGeneration stderr: Loading pipeline components...:  86%|########5 | 6/7 [00:11<00:01,  1.27s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:25"}
Loading pipeline components...: 100%|##########| 7/7 [00:11<00:00,  1.60s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:25"}
info: ImageGeneration stdout: Successfully loaded stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-09 22:32:34"}
info: ImageGeneration stdout: Starting image generation... {"service":"user-service","timestamp":"2025-07-09 22:32:34"}
info: ImageGeneration stdout: Generating image with prompt: 'an old barn interior scene' {"service":"user-service","timestamp":"2025-07-09 22:32:34"}
info: ImageGeneration stdout: Parameters: 1600x1024, steps=20, guidance=7.5, seed=179611904 {"service":"user-service","timestamp":"2025-07-09 22:32:34"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-09 22:32:34"}
info: ImageGeneration stderr: 0%|          | 0/20 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-09 22:32:36"}
info: ImageGeneration stderr: 5%|5         | 1/20 [00:05<01:42,  5.38s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:41"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:32:46"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:32:46"}
info: ImageGeneration stderr: 10%|#         | 2/20 [00:14<02:17,  7.66s/it] {"service":"user-service","timestamp":"2025-07-09 22:32:50"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:32:56"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:32:56"}
info: ImageGeneration stderr: 15%|#5        | 3/20 [00:24<02:27,  8.66s/it] {"service":"user-service","timestamp":"2025-07-09 22:33:00"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:33:06"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:33:06"}
info: ImageGeneration stderr: 20%|##        | 4/20 [00:34<02:27,  9.24s/it] {"service":"user-service","timestamp":"2025-07-09 22:33:10"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:33:16"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:33:16"}
info: ImageGeneration stderr: 25%|##5       | 5/20 [00:44<02:22,  9.49s/it] {"service":"user-service","timestamp":"2025-07-09 22:33:20"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:33:26"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:33:26"}
info: ImageGeneration stderr: 30%|###       | 6/20 [00:54<02:13,  9.54s/it] {"service":"user-service","timestamp":"2025-07-09 22:33:30"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:33:35"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:33:35"}
info: ImageGeneration stderr: 35%|###5      | 7/20 [01:03<02:04,  9.60s/it] {"service":"user-service","timestamp":"2025-07-09 22:33:39"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:33:45"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:33:45"}
info: ImageGeneration stderr: 40%|####      | 8/20 [01:13<01:55,  9.61s/it] {"service":"user-service","timestamp":"2025-07-09 22:33:49"}
info: ImageGeneration stdout: Using VAE scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-09 22:33:54"}
info: Received preview image, base64 length: 4420 {"service":"user-service","timestamp":"2025-07-09 22:33:54"}
