const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const net = require('net');
const axios = require('axios');
const FormData = require('form-data');
const chalk = require('chalk');
const logger = require('./logger');
chalk.level = 3; // Force 16m color support for red logs

// Configuration constants
const HUNYAUN_PORT = 7960; // Use default port for now to avoid conflicts
const HUNYAUN_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/hunyuan2-spz-101/run-projectorz_(faster)/run-stableprojectorz-full-multiview.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

// Connection attempt counter to reduce log spam
let connectionAttemptCounter = 0;
let recoveryAttemptCounter = 0;
const MAX_RECOVERY_ATTEMPTS = 3;

// Global progress callback for server output parsing
let globalProgressCallback = null;

// Stage tracking for progress reporting
let currentStage = 'preprocessing';
let stageCompleted = {
  preprocessing: false,
  structure_generation: false,
  glb_generation: false,
  completion: false
};
let progressLineCounter = 0;

// Automated Python process cleanup function
async function cleanupPythonProcesses() {
  logHunyaunServer('Checking for lingering Python processes...');
  
  return new Promise((resolve) => {
    const cleanup = spawn('taskkill', ['/F', '/IM', 'python.exe'], { 
      stdio: 'pipe',
      shell: true 
    });
    
    cleanup.on('close', (code) => {
      if (code === 0) {
        logHunyaunServer('Python processes cleaned up successfully');
      } else {
        logHunyaunServer('No Python processes found to clean up (or cleanup failed)');
      }
      resolve();
    });
    
    cleanup.on('error', (error) => {
      logHunyaunServer('Error during Python cleanup:', error.message);
      resolve(); // Continue even if cleanup fails
    });
  });
}

const logHunyaunServer = (...args) => {
  const prefix = chalk.red('[HunyaunServer]');
  // Only use console.log to avoid logger character separation issue
  console.log(prefix, ...args);
};

// Check if Hunyaun server is running
async function isHunyaunRunning() {
  if (connectionAttemptCounter % 10 === 0 && connectionAttemptCounter > 0) {
    logHunyaunServer(`Connection check #${connectionAttemptCounter}`);
  }

  return new Promise((resolve) => {
    const socket = net.createConnection(HUNYAUN_PORT, HUNYAUN_HOST);
    socket.on('connect', () => {
      logHunyaunServer('Server is running - connection successful');
      connectionAttemptCounter = 0; // Reset counter on successful connection
      socket.end();
      resolve(true);
    });
    socket.on('error', (error) => {
      connectionAttemptCounter++;
      // Only log every 10th connection failure to reduce spam
      if (connectionAttemptCounter % 10 === 0) {
        logHunyaunServer('Server not running - connection failed (' + connectionAttemptCounter + ' attempts):', error.code);
      }

      // Auto-recovery: If we've failed 200 times, try to restart the server (Hunyuan takes longer to start)
      if (connectionAttemptCounter >= 200 && recoveryAttemptCounter < MAX_RECOVERY_ATTEMPTS) {
        logHunyaunServer('🔄 Auto-recovery triggered after 200 failed attempts (attempt ' + (recoveryAttemptCounter + 1) + '/' + MAX_RECOVERY_ATTEMPTS + ')');
        triggerServerRestart();
      } else if (connectionAttemptCounter >= 200 && recoveryAttemptCounter >= MAX_RECOVERY_ATTEMPTS) {
        logHunyaunServer('❌ Max recovery attempts (' + MAX_RECOVERY_ATTEMPTS + ') reached. Server may be permanently stuck.');
      }

      resolve(false);
    });
  });
}

function startHunyaunServer(progressCb = null) {
  logHunyaunServer('Starting server...');
  logHunyaunServer('RUN_BAT:', RUN_BAT);
  logHunyaunServer('Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Hunyaun run batch file not found at: ' + RUN_BAT);
  }

  // Kill any existing process first
  if (global.hunyaunProcess) {
    logHunyaunServer('Killing existing Hunyaun process...');
    global.hunyaunProcess.kill('SIGTERM');
    global.hunyaunProcess = null;
  }

  // Run the original batch file directly (using default port 7960)
  // Use shell: true to properly handle paths with spaces
  const process = spawn(`"${RUN_BAT}"`, [], {
    cwd: path.dirname(RUN_BAT),
    stdio: 'pipe',
    shell: true,
    windowsHide: true
  });

  global.hunyaunProcess = process;

  process.stdout.on('data', (data) => {
    const output = data.toString();
    parseProgressFromOutput(output, progressCb);
    
    // Reduce log verbosity - only show every 10th line or important messages
    const lines = output.split('\n').filter(line => line.trim());
    lines.forEach((line, index) => {
      if (line.includes('ERROR') || line.includes('WARNING') ||
          line.includes('Server is active') || line.includes('listening on') ||
          line.includes('Loading pipeline') || line.includes('Initializing') ||
          index % 10 === 0) {
        logHunyaunServer('[STDOUT]', line);
      }
    });
  });

  process.stderr.on('data', (data) => {
    const output = data.toString();
    parseProgressFromOutput(output, progressCb);
    
    // Filter out repetitive ECONNREFUSED messages
    if (!output.includes('ECONNREFUSED')) {
      logHunyaunServer('[STDERR]', output);
    }
  });

  process.on('close', (code) => {
    logHunyaunServer('Hunyaun server process exited with code:', code);
    global.hunyaunProcess = null;
  });

  process.on('error', (error) => {
    logHunyaunServer('Error starting Hunyaun server:', error);
    global.hunyaunProcess = null;
  });

  logHunyaunServer('Hunyaun server process started');
}

// Wait for Hunyaun server to be ready
async function waitForHunyaunReady(timeout = null, progressCb = null) {
  logHunyaunServer('Waiting for Hunyaun server to be ready...');
  
  let waitCounter = 0;
  let recoveryTriggered = false;

  while (true) {
    if (await isHunyaunRunning()) return true;

    waitCounter++;

    // Check if auto-recovery was triggered (connection counter was reset)
    if (connectionAttemptCounter < 10 && waitCounter > 150 && !recoveryTriggered) {
      logHunyaunServer('🔄 Auto-recovery detected, resetting wait counter...');
      waitCounter = 0;
      recoveryTriggered = true;
    }

    // Only send progress callback every 5th check (every 10 seconds) to reduce spam
    if (progressCb && waitCounter % 5 === 0) {
      const message = recoveryTriggered ?
        'Server restarted, waiting for connection...' :
        'Waiting for Hunyaun server to start...';
      progressCb({ stage: 'hunyaun', message });
    }

    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever, but with auto-recovery
  }
}

// Auto-recovery function to restart the server after failed attempts
async function triggerServerRestart() {
  try {
    recoveryAttemptCounter++;
    logHunyaunServer('🔧 Starting auto-recovery process (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')...');

    // Step 1: Kill existing Python processes
    logHunyaunServer('🧹 Cleaning up Python processes...');
    await cleanupPythonProcesses();

    // Step 2: Kill existing Hunyaun process if it exists
    if (global.hunyaunProcess) {
      logHunyaunServer('🛑 Terminating existing Hunyaun process...');
      global.hunyaunProcess.kill('SIGTERM');
      global.hunyaunProcess = null;
    }

    // Step 3: Wait a moment for cleanup
    logHunyaunServer('⏳ Waiting for cleanup to complete...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 4: Reset connection counter and restart server
    connectionAttemptCounter = 0;
    logHunyaunServer('🚀 Restarting Hunyaun server...');
    startHunyaunServer();

    logHunyaunServer('✅ Auto-recovery process completed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')');

  } catch (error) {
    logHunyaunServer('❌ Auto-recovery failed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + '):', error);
  }
}

// Reset stage tracking for new generation
function resetStageTracking() {
  currentStage = 'preprocessing';
  stageCompleted = {
    preprocessing: false,
    structure_generation: false,
    glb_generation: false,
    completion: false
  };
  progressLineCounter = 0; // Reset progress line counter
  recoveryAttemptCounter = 0; // Reset recovery counter on successful new generation
  logHunyaunServer('[Hunyaun Progress] Stage tracking reset for new generation');
}

function parseProgressFromOutput(output, progressCb) {
  if (!progressCb) {
    return;
  }

  const lines = output.split('\n');

  for (const line of lines) {
    progressLineCounter++;

    // Only process every 10th line to reduce spam
    if (progressLineCounter % 10 !== 0) {
      continue;
    }

    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    // Map Hunyaun output to progress stages
    const { stage, description } = mapHunyaunMessageToStage(trimmedLine, 0);

    // Determine progress based on stage
    let progress = 0;
    if (stageCompleted.preprocessing) progress = 25;
    if (stageCompleted.structure_generation) progress = 50;
    if (stageCompleted.glb_generation) progress = 75;
    if (stageCompleted.completion) progress = 100;

    progressCb({ stage, progress, message: description });
  }
}

function mapHunyaunMessageToStage(message, progress) {
  const lowerMessage = message.toLowerCase();

  // Stage 1: Preprocessing
  if (lowerMessage.includes('loading') || lowerMessage.includes('initializing') ||
      lowerMessage.includes('preprocessing') || lowerMessage.includes('image processing')) {
    if (!stageCompleted.preprocessing) {
      currentStage = 'preprocessing';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {
        stageCompleted.preprocessing = true;
      }
    }
    return { stage: 'hunyaun_preprocessing', description: 'Image Preprocessing: Preparing image for 3D generation' };
  }

  // Stage 2: 3D Structure Generation
  if (lowerMessage.includes('generating 3d') || lowerMessage.includes('structure') ||
      lowerMessage.includes('inference') || lowerMessage.includes('diffusion')) {
    if (!stageCompleted.structure_generation) {
      currentStage = 'structure_generation';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done') ||
          lowerMessage.includes('generated')) {
        stageCompleted.structure_generation = true;
      }
    }
    return { stage: 'hunyaun_generation', description: 'Hunyaun3D Generation: Creating 3D structure from image' };
  }

  // Stage 3: GLB Generation
  if (lowerMessage.includes('glb') || lowerMessage.includes('mesh') ||
      lowerMessage.includes('export') || lowerMessage.includes('texture')) {
    if (!stageCompleted.glb_generation) {
      currentStage = 'glb_generation';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done') ||
          lowerMessage.includes('exported')) {
        stageCompleted.glb_generation = true;
      }
    }
    return { stage: 'hunyaun_export', description: 'GLB Export: Finalizing 3D model file' };
  }

  // Stage 4: Completion
  if (lowerMessage.includes('complete') || lowerMessage.includes('finished') ||
      lowerMessage.includes('success')) {
    stageCompleted.completion = true;
    return { stage: 'hunyaun_complete', description: 'Generation Complete: 3D model ready' };
  }

  // Default stage based on current stage
  const stageMap = {
    'preprocessing': { stage: 'hunyaun_preprocessing', description: 'Image Preprocessing: Preparing image for 3D generation' },
    'structure_generation': { stage: 'hunyaun_generation', description: 'Hunyaun3D Generation: Creating 3D structure from image' },
    'glb_generation': { stage: 'hunyaun_export', description: 'GLB Export: Finalizing 3D model file' },
    'completion': { stage: 'hunyaun_complete', description: 'Generation Complete: 3D model ready' }
  };

  return stageMap[currentStage] || { stage: 'hunyaun', description: 'Processing...' };
}

// Main 3D generation function
async function generate3DModel(imagePath, progressCb, settings = {}) {
  logHunyaunServer('generate3DModel called with imagePath:', imagePath);
  logHunyaunServer('Settings received:', settings);

  // First, cleanup any lingering Python processes to prevent permission errors
  await cleanupPythonProcesses();

  const isRunning = await isHunyaunRunning();
  logHunyaunServer('isHunyaunRunning() returned:', isRunning);

  // Reset stage tracking for new generation
  resetStageTracking();

  // Set the global progress callback for server output parsing
  globalProgressCallback = progressCb;

  if (!isRunning) {
    logHunyaunServer('Server not running, starting server...');
    if (progressCb) {
      const { stage, description } = mapHunyaunMessageToStage('Starting Hunyaun server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startHunyaunServer(progressCb);
      logHunyaunServer('startHunyaunServer() called successfully');
    } catch (error) {
      logHunyaunServer('Error starting server:', error);
      throw error;
    }
    await waitForHunyaunReady(null, progressCb);
  } else {
    logHunyaunServer('Server already running, skipping startup');
  }

  if (progressCb) {
    const { stage, description } = mapHunyaunMessageToStage('Sending image to Hunyaun for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }

  // Read and encode the image
  const imageBuffer = fs.readFileSync(imagePath);
  const imageBase64 = imageBuffer.toString('base64');
  const imageDataUrl = `data:image/png;base64,${imageBase64}`;

  // Map UI settings to API parameters with research-based optimal defaults
  const apiSettings = {
    // Use community-recommended optimal values for high quality
    guidance_scale: settings.guidance_scale || 5.5,  // Official API default (5.5)

    // Research-based quality settings
    num_inference_steps: Math.max(settings.num_inference_steps || 30, 30), // Official default (30) for best quality
    octree_resolution: Math.max(settings.octree_resolution || 384, 384),   // Community sweet spot (384)
    texture_size: settings.texture_size || 2048,

    // Conservative mesh simplification for shape preservation
    mesh_simplify_ratio: Math.min(settings.simplify || 0.1, 0.1),

    // Map UI face_count to API num_chunks (divided by 1000 since API multiplies by 1000)
    // Use optimal range: 5k-40k faces as recommended by community
    num_chunks: settings.face_count ?
      Math.min(Math.max(Math.floor(settings.face_count / 1000), 5), 100) : 40, // Default 40k faces

    // Map UI enable_texture to API apply_texture
    apply_texture: settings.enable_texture || false,
  };

  logHunyaunServer('Mapped API settings:', apiSettings);

  // Prepare the request data for Hunyaun API
  const requestData = {
    single_multi_img_input: [imageDataUrl],
    seed: Math.floor(Math.random() * 1000000),
    guidance_scale: apiSettings.guidance_scale,
    num_inference_steps: apiSettings.num_inference_steps,
    octree_resolution: apiSettings.octree_resolution,
    num_chunks: apiSettings.num_chunks,
    mesh_simplify_ratio: apiSettings.mesh_simplify_ratio,
    texture_size: apiSettings.texture_size,
    apply_texture: apiSettings.apply_texture,
    output_format: "glb"
  };

  logHunyaunServer('Final request data:', {
    ...requestData,
    single_multi_img_input: ['[IMAGE_DATA_OMITTED]'] // Don't log the huge base64 image
  });

  try {
    // Send generation request to Hunyaun server
    logHunyaunServer('Sending generation request to Hunyaun server...');
    const response = await axios.post(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/generate`, requestData, {
      timeout: 300000, // 5 minute timeout
      headers: {
        'Content-Type': 'application/json'
      }
    });

    logHunyaunServer('Generation request sent successfully');

    // Poll for status updates
    let statusComplete = false;
    while (!statusComplete) {
      try {
        const statusResp = await axios.get(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/status`, { timeout: 10000 });
        const statusData = statusResp.data;

        if (progressCb && statusData) {
          progressCb({
            stage: 'hunyaun',
            progress: statusData.progress || 0,
            message: statusData.message || 'Processing...'
          });

          if (statusData.status === 'COMPLETE' || statusData.progress >= 100) {
            statusComplete = true;
          }
        }
      } catch (err) {
        logHunyaunServer('Error polling /status endpoint:', err.message);
        // Continue polling
      }

      if (!statusComplete) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before next poll
      }
    }

    // Download the generated model
    logHunyaunServer('Downloading generated model...');
    const modelResponse = await axios.get(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/download/model`, {
      responseType: 'stream',
      timeout: 60000
    });

    // Save the model to output directory
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputPath = path.join(OUTPUT_DIR, `hunyaun_model_${timestamp}.glb`);

    // Ensure output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        logHunyaunServer('Model saved to:', outputPath);
        if (progressCb) {
          const { stage, description } = mapHunyaunMessageToStage('3D model downloaded.', 100);
          progressCb({ stage, progress: 100, message: description });
        }
        const app = require('electron').app;
        const relativePath = path.relative(app.getAppPath(), outputPath);
        resolve(relativePath);
      });
      writer.on('error', reject);
    });

  } catch (error) {
    logHunyaunServer('Error during 3D generation:', error);
    throw error;
  }
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses
};
