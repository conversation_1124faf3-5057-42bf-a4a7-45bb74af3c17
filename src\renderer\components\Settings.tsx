import React from 'react';
import { Settings as Setting<PERSON><PERSON><PERSON>, Sliders, ChevronDown, ChevronUp, Cpu } from 'lucide-react';

interface SettingsProps {
  onSettingsChange: (settings: ModelSettings) => void;
  isDarkMode: boolean;
  initialSettings?: ModelSettings;
  selectedPipeline?: string;
  onPipelineChange?: (pipeline: string) => void;
  availablePipelines?: Pipeline[];
  selectedModel?: string;
  onModelChange?: (model: string) => void;
}

interface Pipeline {
  id: string;
  name: string;
  description: string;
  available: boolean;
  features: string[];
  models?: { name: string; repo_id?: string }[];
}

interface ModelSettings {
  ss_steps: number;
  ss_cfg_strength: number;
  slat_steps: number;
  slat_cfg_strength: number;
  seed?: number;
  randomize_seed?: boolean;
  simplify: number;
  texture_size: number;
  enable_lighting_optimizer?: boolean;
  // Hunyuan3D-2 specific settings
  octree_resolution?: number;
  num_inference_steps?: number;
  guidance_scale?: number;
  enable_texture?: boolean;
  face_count?: number;
  // Advanced Hunyuan3D-2 settings
  mesh_simplify_ratio?: number;
  num_chunks?: number;
}

export const Settings: React.FC<SettingsProps> = ({
  onSettingsChange,
  isDarkMode,
  initialSettings,
  selectedPipeline = 'Microsoft_TRELLIS',
  onPipelineChange,
  availablePipelines = [],
  selectedModel,
  onModelChange,
}) => {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [isAdvancedExpanded, setIsAdvancedExpanded] = React.useState(false);
  const [settings, setSettings] = React.useState<ModelSettings>(initialSettings || {
    ss_steps: 12,
    ss_cfg_strength: 7.5,
    slat_steps: 12,
    slat_cfg_strength: 3.0,
    randomize_seed: true,
    seed: Math.floor(Math.random() * 1000000),
    simplify: 0.95,
    texture_size: 1024,
    enable_lighting_optimizer: true,
    // Hunyuan3D-2 balanced defaults (research-based stable settings)
    octree_resolution: 256,
    num_inference_steps: 25,
    guidance_scale: 5.5,
    enable_texture: false,
    face_count: 30000,
    // Advanced settings with balanced defaults
    mesh_simplify_ratio: 0.15,
    num_chunks: 30
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const target = e.target as HTMLInputElement;
    const { name, value, type } = target;
    const checked = target.checked;

    let newValue: any;
    if (type === 'checkbox') {
      newValue = checked;
    } else if (name === 'texture_size') {
      // Convert texture size to number
      newValue = parseInt(value);
    } else if (name === 'seed') {
      // Convert seed to number
      newValue = parseInt(value);
    } else {
      // Default to float for numeric inputs
      newValue = parseFloat(value);
    }

    const updatedSettings = {
      ...settings,
      [name]: newValue,
    };

    // If randomize_seed is turned off, ensure we have a seed value
    if (name === 'randomize_seed' && !newValue && !updatedSettings.seed) {
      updatedSettings.seed = Math.floor(Math.random() * 1000000);
    }

    // If randomize_seed is turned on, we can remove the seed value
    if (name === 'randomize_seed' && newValue) {
      updatedSettings.seed = undefined;
    }

    setSettings(updatedSettings);
    onSettingsChange(updatedSettings);
  };

  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-full flex items-center justify-between gap-2 mb-4 text-left hover:${
          isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
        } rounded-lg p-2 -m-2 transition-colors`}
      >
        <div className="flex items-center gap-2">
          <SettingsIcon className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Generation Settings
          </h3>
        </div>
        {isExpanded ? (
          <ChevronUp className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
        ) : (
          <ChevronDown className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
        )}
      </button>

      {!isExpanded && (
        <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
          Steps: {settings.ss_steps}/{settings.slat_steps} • Guidance: {settings.ss_cfg_strength}/{settings.slat_cfg_strength} • Texture: {settings.texture_size}px
        </div>
      )}

      {isExpanded && (
        <div className="space-y-4 animate-in slide-in-from-top-2 duration-200">

        {/* Pipeline Selection */}
        {availablePipelines.length > 0 && onPipelineChange && (
          <div className="mb-4 border-b border-gray-200 dark:border-gray-700 pb-4">
            <label className={`flex items-center gap-2 text-sm font-medium mb-2 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Cpu className="w-4 h-4" />
              3D Generation Pipeline
            </label>
            <select
              value={selectedPipeline}
              onChange={(e) => onPipelineChange(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg ${
                isDarkMode ? 'bg-gray-700 text-white border-gray-600' : 'bg-gray-100 border-gray-300'
              } border focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              {availablePipelines.map((pipeline) => (
                <option
                  key={pipeline.id}
                  value={pipeline.id}
                  disabled={!pipeline.available}
                >
                  {pipeline.name} {!pipeline.available ? '(Not Available)' : ''}
                </option>
              ))}
            </select>
            {availablePipelines.find(p => p.id === selectedPipeline) && (
              <div className={`mt-2 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {availablePipelines.find(p => p.id === selectedPipeline)?.description}
                <div className="flex flex-wrap gap-1 mt-1">
                  {availablePipelines.find(p => p.id === selectedPipeline)?.features.map((feature, index) => (
                    <span
                      key={index}
                      className={`px-2 py-1 rounded text-xs ${
                        isDarkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                      }`}
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Model selection if pipeline provides models */}
        {availablePipelines.find(p => p.id === selectedPipeline)?.models?.length && onModelChange && (
          <div className="mt-4">
            <label className={`flex items-center gap-2 text-sm font-medium mb-2 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Model
            </label>
            <select
              value={selectedModel}
              onChange={(e) => onModelChange(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg ${
                isDarkMode ? 'bg-gray-700 text-white border-gray-600' : 'bg-gray-100 border-gray-300'
              } border focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              {availablePipelines.find(p => p.id === selectedPipeline)!.models!.map(m => (
                <option key={m.name} value={m.name}>{m.name}</option>
              ))}
            </select>
          </div>
        )}

        <div className="mb-4">
          <label className={`flex items-center gap-2 text-sm font-medium ${
            isDarkMode ? 'text-gray-300' : 'text-gray-700'
          }`}>
            <input
              type="checkbox"
              name="randomize_seed"
              checked={settings.randomize_seed}
              onChange={handleChange}
              className="mr-1"
            />
            Randomize Seed
          </label>
          {!settings.randomize_seed && (
            <input
              type="number"
              name="seed"
              value={settings.seed || 0}
              onChange={handleChange}
              className={`mt-2 w-full px-3 py-2 rounded-lg ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-100'}`}
            />
          )}
        </div>

        {/* Pipeline-specific settings */}
        {selectedPipeline.toLowerCase().includes('hunyaun') ? (
          // Hunyuan3D-2 specific settings
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Hunyuan3D-2 Generation Settings
            </h4>

            <div className="mb-3">
              <label className={`flex items-center gap-2 text-sm font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <Sliders className="w-4 h-4" />
                Octree Resolution
              </label>
              <input
                type="range"
                name="octree_resolution"
                min="128"
                max="512"
                step="64"
                value={settings.octree_resolution}
                onChange={handleChange}
                className={`w-full h-2 ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                } rounded-lg appearance-none cursor-pointer`}
              />
              <div className="flex justify-between text-xs mt-1">
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.octree_resolution}</span>
                <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more detail</span>
              </div>
            </div>

            <div className="mb-3">
              <label className={`flex items-center gap-2 text-sm font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <Sliders className="w-4 h-4" />
                Inference Steps
              </label>
              <input
                type="range"
                name="num_inference_steps"
                min="10"
                max="50"
                step="5"
                value={settings.num_inference_steps}
                onChange={handleChange}
                className={`w-full h-2 ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                } rounded-lg appearance-none cursor-pointer`}
              />
              <div className="flex justify-between text-xs mt-1">
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.num_inference_steps}</span>
                <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = better quality</span>
              </div>
            </div>

            <div className="mb-3">
              <label className={`flex items-center gap-2 text-sm font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <Sliders className="w-4 h-4" />
                Guidance Scale
              </label>
              <input
                type="range"
                name="guidance_scale"
                min="1.0"
                max="15.0"
                step="0.5"
                value={settings.guidance_scale}
                onChange={handleChange}
                className={`w-full h-2 ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                } rounded-lg appearance-none cursor-pointer`}
              />
              <div className="flex justify-between text-xs mt-1">
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.guidance_scale}</span>
                <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more adherence</span>
              </div>
            </div>

            <div className="mb-4">
              <label className={`flex items-center gap-2 text-sm font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <input
                  type="checkbox"
                  name="enable_texture"
                  checked={settings.enable_texture}
                  onChange={handleChange}
                  className="mr-1"
                />
                Enable Texture Generation
              </label>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Apply textures to the generated 3D model (slower but higher quality)
              </p>
            </div>

            {settings.enable_texture && (
              <div className="mb-3">
                <label className={`flex items-center gap-2 text-sm font-medium ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  <Sliders className="w-4 h-4" />
                  Max Face Count
                </label>
                <input
                  type="range"
                  name="face_count"
                  min="10000"
                  max="100000"
                  step="10000"
                  value={settings.face_count}
                  onChange={handleChange}
                  className={`w-full h-2 ${
                    isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                  } rounded-lg appearance-none cursor-pointer`}
                />
                <div className="flex justify-between text-xs mt-1">
                  <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.face_count}</span>
                  <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more detail</span>
                </div>
              </div>
            )}

            {/* Advanced Settings Dropdown */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
              <button
                onClick={() => setIsAdvancedExpanded(!isAdvancedExpanded)}
                className={`flex items-center justify-between w-full text-sm font-medium ${
                  isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900'
                } transition-colors`}
              >
                <div className="flex items-center gap-2">
                  <Cpu className="w-4 h-4" />
                  Advanced Settings
                </div>
                {isAdvancedExpanded ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </button>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Fine-tune generation parameters for expert users
              </p>

              {isAdvancedExpanded && (
                <div className="mt-4 space-y-3">
                  <div className="mb-3">
                    <label className={`flex items-center gap-2 text-sm font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <Sliders className="w-4 h-4" />
                      Mesh Simplification Ratio
                    </label>
                    <input
                      type="range"
                      name="mesh_simplify_ratio"
                      min="0.05"
                      max="0.5"
                      step="0.05"
                      value={settings.mesh_simplify_ratio}
                      onChange={handleChange}
                      className={`w-full h-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      } rounded-lg appearance-none cursor-pointer`}
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.mesh_simplify_ratio}</span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Lower = more detail</span>
                    </div>
                    <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Controls how much the mesh geometry is simplified (0.15 recommended)
                    </p>
                  </div>

                  <div className="mb-3">
                    <label className={`flex items-center gap-2 text-sm font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <Sliders className="w-4 h-4" />
                      Processing Chunks
                    </label>
                    <input
                      type="range"
                      name="num_chunks"
                      min="10"
                      max="100"
                      step="10"
                      value={settings.num_chunks}
                      onChange={handleChange}
                      className={`w-full h-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      } rounded-lg appearance-none cursor-pointer`}
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.num_chunks}k faces</span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more detail</span>
                    </div>
                    <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Number of processing chunks (affects final mesh density, 30k recommended)
                    </p>
                  </div>

                  <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-blue-900/20 border border-blue-800/30' : 'bg-blue-50 border border-blue-200'}`}>
                    <p className={`text-xs ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}`}>
                      <strong>💡 Tip:</strong> Current settings are balanced for stability and quality.
                      Higher values may cause crashes during texture generation.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          // Trellis specific settings
          <>
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Stage 1: Sparse Structure Generation
            </h4>

          <div className="mb-3">
            <label className={`flex items-center gap-2 text-sm font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Sliders className="w-4 h-4" />
              Guidance Strength
            </label>
            <input
              type="range"
              name="ss_cfg_strength"
              min="0"
              max="10"
              step="0.1"
              value={settings.ss_cfg_strength}
              onChange={handleChange}
              className={`w-full h-2 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
              } rounded-lg appearance-none cursor-pointer`}
            />
            <div className="flex justify-between text-xs mt-1">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.ss_cfg_strength}</span>
            </div>
          </div>

          <div className="mb-3">
            <label className={`flex items-center gap-2 text-sm font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Sliders className="w-4 h-4" />
              Sampling Steps
            </label>
            <input
              type="range"
              name="ss_steps"
              min="1"
              max="50"
              step="1"
              value={settings.ss_steps}
              onChange={handleChange}
              className={`w-full h-2 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
              } rounded-lg appearance-none cursor-pointer`}
            />
            <div className="flex justify-between text-xs mt-1">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.ss_steps}</span>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Stage 2: Structured Latent Generation
          </h4>

          <div className="mb-3">
            <label className={`flex items-center gap-2 text-sm font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Sliders className="w-4 h-4" />
              Guidance Strength
            </label>
            <input
              type="range"
              name="slat_cfg_strength"
              min="0"
              max="10"
              step="0.1"
              value={settings.slat_cfg_strength}
              onChange={handleChange}
              className={`w-full h-2 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
              } rounded-lg appearance-none cursor-pointer`}
            />
            <div className="flex justify-between text-xs mt-1">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.slat_cfg_strength}</span>
            </div>
          </div>

          <div className="mb-3">
            <label className={`flex items-center gap-2 text-sm font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Sliders className="w-4 h-4" />
              Sampling Steps
            </label>
            <input
              type="range"
              name="slat_steps"
              min="1"
              max="50"
              step="1"
              value={settings.slat_steps}
              onChange={handleChange}
              className={`w-full h-2 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
              } rounded-lg appearance-none cursor-pointer`}
            />
            <div className="flex justify-between text-xs mt-1">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.slat_steps}</span>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Output Settings
          </h4>

          <div className="mb-4">
            <label className={`flex items-center gap-2 text-sm font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <input
                type="checkbox"
                name="enable_lighting_optimizer"
                checked={settings.enable_lighting_optimizer}
                onChange={handleChange}
                className="mr-1"
              />
              Enable Lighting Optimizer
            </label>
            <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Reduces dark areas and shadows in the 3D model for better lighting
            </p>
          </div>



          <div className="mb-3">
            <label className={`flex items-center gap-2 text-sm font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Sliders className="w-4 h-4" />
              Mesh Simplification
            </label>
            <input
              type="range"
              name="simplify"
              min="0.9"
              max="0.98"
              step="0.01"
              value={settings.simplify}
              onChange={handleChange}
              className={`w-full h-2 ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
              } rounded-lg appearance-none cursor-pointer`}
            />
            <div className="flex justify-between text-xs mt-1">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.simplify}</span>
            </div>
          </div>

          <div className="mb-3">
            <label className={`flex items-center gap-2 text-sm font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Sliders className="w-4 h-4" />
              Texture Size
            </label>
            <select
              name="texture_size"
              value={settings.texture_size}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-100'}`}
            >
              <option value="512">512 x 512</option>
              <option value="1024">1024 x 1024</option>
              <option value="2048">2048 x 2048</option>
            </select>
          </div>
        </div>
          </>
        )}
        </div>
      )}
    </div>
  );
};