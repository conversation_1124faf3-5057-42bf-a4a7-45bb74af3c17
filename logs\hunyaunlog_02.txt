========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2111 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-Cx54nlL0.css     34.87 kB │ gzip:   6.16 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DpOcXYbZ.js   1,267.86 kB │ gzip: 349.82 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.41s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
[Trellis Server] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:04:57"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:04:57"}
[Trellis Server] Found Python processes, killing them...
[Trellis Server] Processes found: python.exe                   22872 Console                    1 15,989,816 K
[Trellis Server] Successfully killed Python processes
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-09 16:05:02"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-09 16:05:03"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-09 16:05:04"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-09 16:05:04"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:05:04"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-09 16:05:06"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-09 16:05:06"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-09 16:05:06"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-09 16:05:09"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-09 16:05:09"}
info: [upload-file] Received ΓÇô filename: a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece.png, size: 11572569 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-09 16:05:26"}
info: Buffer received (bytes): 11572569 {"service":"user-service","timestamp":"2025-07-09 16:05:26"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\0a7d96ab-083d-43b8-80ae-b4971a0be9d2\a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece.png {"service":"user-service","timestamp":"2025-07-09 16:05:26"}
info: Starting background removal for N:\3D AI Studio\uploads\0a7d96ab-083d-43b8-80ae-b4971a0be9d2\a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece.png... {"service":"user-service","timestamp":"2025-07-09 16:05:26"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\0a7d96ab-083d-43b8-80ae-b4971a0be9d2\a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece.png N:\3D AI Studio\uploads\0a7d96ab-083d-43b8-80ae-b4971a0be9d2\a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece_processed.png {"service":"user-service","timestamp":"2025-07-09 16:05:26"}
info: [load-file] Received request for: uploads/0a7d96ab-083d-43b8-80ae-b4971a0be9d2/a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece_processed.png {"service":"user-service","timestamp":"2025-07-09 16:05:34"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\0a7d96ab-083d-43b8-80ae-b4971a0be9d2\a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece_processed.png {"service":"user-service","timestamp":"2025-07-09 16:05:34"}
info: IPC: run-pipeline called for: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:05:44"}
info: runPipeline request: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:05:44"}
info: runPipeline: Registered pipelines at call time: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:05:44"}
info: Checking dependencies for Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:05:44"}
warn: Python dependencies not satisfied for Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:05:44"}
[IPC Handler] Sending status: hunyaun - 25% - Launching Hunyaun server and generating 3D model...
[HunyaunServer] generate3DModel called with imagePath: uploads/0a7d96ab-083d-43b8-80ae-b4971a0be9d2/a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png_Cut_Log_Piece_processed.png
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] isHunyaunRunning() returned: false
[HunyaunServer] [Hunyaun Progress] Stage tracking reset for new generation
[HunyaunServer] Server not running, starting server...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] startHunyaunServer() called successfully
[HunyaunServer] Waiting for Hunyaun server to be ready...
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] Creating virtual environment using portable Python...
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe" -m virtualenv "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] sitecustomize.py applied
[HunyaunServer] [STDOUT]         1 file(s) copied.
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 16:05:52"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (10 attempts): ECONNREFUSED
[HunyaunServer] Connection check #10
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[HunyaunServer] [STDERR] 2025-07-09 16:06:12,671 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv

[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-09 16:06:12,672 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (20 attempts): ECONNREFUSED
[HunyaunServer] Connection check #20
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]
[HunyaunServer] [STDERR]

[HunyaunServer] [STDOUT] 2025-07-09 16:06:25 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[HunyaunServer] [STDERR] 2025-07-09 16:06:25,783 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (30 attempts): ECONNREFUSED
[HunyaunServer] Connection check #30
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 16:06:43"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (40 attempts): ECONNREFUSED
[HunyaunServer] Connection check #40
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 1871.17it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 5760.48it/s]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
Loading pipeline components...:  33%|###3      | 2/6 [00:01<00:02,  1.65it/s]
Loading pipeline components...:  83%|########3 | 5/6 [00:03<00:00,  1.40it/s]
Loading pipeline components...: 100%|##########| 6/6 [00:03<00:00,  1.51it/s]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
Loading pipeline components...:  17%|#6        | 1/6 [00:00<00:00,  9.80it/s]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (50 attempts): ECONNREFUSED
[HunyaunServer] Connection check #50
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  83%|########3 | 5/6 [00:15<00:03,  3.24s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:19<00:00,  3.20s/it]

[HunyaunServer] [STDOUT] 2025-07-09 16:07:31 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
info: [Hunyaun Progress] hunyaun_preprocessing: 0% - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-09 16:07:31"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] ==================================================
[HunyaunServer] [STDOUT] Server is active and listening on 127.0.0.1:7960
[HunyaunServer] Server is running - connection successful
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Sending generation request to Hunyaun server...
[HunyaunServer] [STDOUT] ==================================================
[HunyaunServer] [STDOUT] 2025-07-09 16:07:34 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] 2025-07-09 16:07:34 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
[HunyaunServer] [STDOUT] 2025-07-09 16:07:36 | INFO | hunyuan3d_api | Shape generation model moved to cuda
Diffusion Sampling::   0%|          | 0/20 [00:00<?, ?it/s]
Diffusion Sampling::   5%|5         | 1/20 [00:01<00:26,  1.41s/it]
Diffusion Sampling::  10%|#         | 2/20 [00:01<00:14,  1.26it/s]
Diffusion Sampling::  15%|#5        | 3/20 [00:02<00:15,  1.11it/s]
Diffusion Sampling::  20%|##        | 4/20 [00:03<00:15,  1.05it/s]
Diffusion Sampling::  25%|##5       | 5/20 [00:04<00:14,  1.02it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  30%|###       | 6/20 [00:05<00:13,  1.00it/s]
Diffusion Sampling::  35%|###5      | 7/20 [00:06<00:13,  1.01s/it]
Diffusion Sampling::  40%|####      | 8/20 [00:07<00:12,  1.02s/it]
Diffusion Sampling::  45%|####5     | 9/20 [00:09<00:11,  1.02s/it]
Diffusion Sampling::  50%|#####     | 10/20 [00:10<00:10,  1.03s/it]
Diffusion Sampling::  55%|#####5    | 11/20 [00:11<00:09,  1.03s/it]
Diffusion Sampling::  60%|######    | 12/20 [00:12<00:08,  1.03s/it]
Diffusion Sampling::  65%|######5   | 13/20 [00:13<00:07,  1.03s/it]
Diffusion Sampling::  70%|#######   | 14/20 [00:14<00:06,  1.03s/it]
Diffusion Sampling::  75%|#######5  | 15/20 [00:15<00:05,  1.03s/it]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  80%|########  | 16/20 [00:16<00:04,  1.04s/it]
Diffusion Sampling::  85%|########5 | 17/20 [00:17<00:03,  1.03s/it]
Diffusion Sampling::  90%|######### | 18/20 [00:18<00:02,  1.04s/it]
Diffusion Sampling::  95%|#########5| 19/20 [00:19<00:01,  1.03s/it]
Diffusion Sampling:: 100%|##########| 20/20 [00:20<00:00,  1.02s/it]

Volume Decoding:   0%|          | 0/213 [00:00<?, ?it/s]
Volume Decoding:   0%|          | 1/213 [00:00<00:40,  5.18it/s]
Volume Decoding:  20%|#9        | 42/213 [00:00<00:00, 176.60it/s]
Volume Decoding:  31%|###1      | 67/213 [00:03<00:09, 15.74it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 16:08:10"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  38%|###8      | 81/213 [00:05<00:10, 12.06it/s]
Volume Decoding:  42%|####2     | 90/213 [00:06<00:11, 10.73it/s]
Volume Decoding:  45%|####5     | 96/213 [00:07<00:11, 10.00it/s]
Volume Decoding:  47%|####6     | 100/213 [00:08<00:11,  9.57it/s]
Volume Decoding:  48%|####8     | 103/213 [00:08<00:11,  9.24it/s]
Volume Decoding:  50%|####9     | 106/213 [00:08<00:12,  8.89it/s]
Volume Decoding:  51%|#####     | 108/213 [00:09<00:12,  8.67it/s]
Volume Decoding:  52%|#####1    | 110/213 [00:09<00:12,  8.46it/s]
Volume Decoding:  53%|#####2    | 112/213 [00:09<00:12,  8.25it/s]
Volume Decoding:  54%|#####3    | 114/213 [00:09<00:12,  8.08it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  54%|#####3    | 115/213 [00:10<00:12,  7.94it/s]
Volume Decoding:  54%|#####4    | 116/213 [00:10<00:12,  7.81it/s]
Volume Decoding:  55%|#####4    | 117/213 [00:10<00:12,  7.85it/s]
Volume Decoding:  55%|#####5    | 118/213 [00:10<00:12,  7.67it/s]
Volume Decoding:  56%|#####5    | 119/213 [00:10<00:12,  7.54it/s]
Volume Decoding:  56%|#####6    | 120/213 [00:10<00:12,  7.61it/s]
Volume Decoding:  57%|#####6    | 121/213 [00:10<00:12,  7.46it/s]
Volume Decoding:  57%|#####7    | 122/213 [00:11<00:12,  7.56it/s]
Volume Decoding:  58%|#####7    | 123/213 [00:11<00:12,  7.39it/s]
Volume Decoding:  58%|#####8    | 124/213 [00:11<00:11,  7.52it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  59%|#####8    | 125/213 [00:11<00:11,  7.38it/s]
Volume Decoding:  59%|#####9    | 126/213 [00:11<00:11,  7.29it/s]
Volume Decoding:  60%|#####9    | 127/213 [00:11<00:11,  7.22it/s]
Volume Decoding:  60%|######    | 128/213 [00:11<00:11,  7.37it/s]
Volume Decoding:  61%|######    | 129/213 [00:11<00:11,  7.30it/s]
Volume Decoding:  61%|######1   | 130/213 [00:12<00:11,  7.44it/s]
Volume Decoding:  62%|######1   | 131/213 [00:12<00:11,  7.30it/s]
Volume Decoding:  62%|######1   | 132/213 [00:12<00:10,  7.47it/s]
Volume Decoding:  62%|######2   | 133/213 [00:12<00:10,  7.32it/s]
Volume Decoding:  63%|######2   | 134/213 [00:12<00:10,  7.48it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  63%|######3   | 135/213 [00:12<00:10,  7.37it/s]
Volume Decoding:  64%|######3   | 136/213 [00:12<00:10,  7.27it/s]
Volume Decoding:  64%|######4   | 137/213 [00:13<00:10,  7.21it/s]
Volume Decoding:  65%|######4   | 138/213 [00:13<00:10,  7.39it/s]
Volume Decoding:  65%|######5   | 139/213 [00:13<00:10,  7.24it/s]
Volume Decoding:  66%|######5   | 140/213 [00:13<00:09,  7.45it/s]
Volume Decoding:  66%|######6   | 141/213 [00:13<00:09,  7.34it/s]
Volume Decoding:  67%|######6   | 142/213 [00:13<00:09,  7.28it/s]
Volume Decoding:  67%|######7   | 143/213 [00:13<00:09,  7.46it/s]
Volume Decoding:  68%|######7   | 144/213 [00:14<00:09,  7.33it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  68%|######8   | 145/213 [00:14<00:09,  7.47it/s]
Volume Decoding:  69%|######8   | 146/213 [00:14<00:09,  7.31it/s]
Volume Decoding:  69%|######9   | 147/213 [00:14<00:09,  7.21it/s]
Volume Decoding:  69%|######9   | 148/213 [00:14<00:08,  7.41it/s]
Volume Decoding:  70%|######9   | 149/213 [00:14<00:08,  7.27it/s]
Volume Decoding:  70%|#######   | 150/213 [00:14<00:08,  7.45it/s]
Volume Decoding:  71%|#######   | 151/213 [00:14<00:08,  7.36it/s]
Volume Decoding:  71%|#######1  | 152/213 [00:15<00:08,  7.23it/s]
Volume Decoding:  72%|#######1  | 153/213 [00:15<00:08,  7.44it/s]
Volume Decoding:  72%|#######2  | 154/213 [00:15<00:08,  7.36it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 16:08:20"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  73%|#######2  | 155/213 [00:15<00:08,  7.24it/s]
Volume Decoding:  73%|#######3  | 156/213 [00:15<00:07,  7.41it/s]
Volume Decoding:  74%|#######3  | 157/213 [00:15<00:07,  7.28it/s]
Volume Decoding:  74%|#######4  | 158/213 [00:15<00:07,  7.48it/s]
Volume Decoding:  75%|#######4  | 159/213 [00:16<00:07,  7.33it/s]
Volume Decoding:  75%|#######5  | 160/213 [00:16<00:07,  7.23it/s]
Volume Decoding:  76%|#######5  | 161/213 [00:16<00:07,  7.39it/s]
Volume Decoding:  76%|#######6  | 162/213 [00:16<00:06,  7.29it/s]
Volume Decoding:  77%|#######6  | 163/213 [00:16<00:06,  7.47it/s]
Volume Decoding:  77%|#######6  | 164/213 [00:16<00:06,  7.32it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  77%|#######7  | 165/213 [00:16<00:06,  7.26it/s]
Volume Decoding:  78%|#######7  | 166/213 [00:17<00:06,  7.44it/s]
Volume Decoding:  78%|#######8  | 167/213 [00:17<00:06,  7.28it/s]
Volume Decoding:  79%|#######8  | 168/213 [00:17<00:06,  7.44it/s]
Volume Decoding:  79%|#######9  | 169/213 [00:17<00:06,  7.31it/s]
Volume Decoding:  80%|#######9  | 170/213 [00:17<00:05,  7.23it/s]
Volume Decoding:  80%|########  | 171/213 [00:17<00:05,  7.43it/s]
Volume Decoding:  81%|########  | 172/213 [00:17<00:05,  7.33it/s]
Volume Decoding:  81%|########1 | 173/213 [00:17<00:05,  7.26it/s]
Volume Decoding:  82%|########1 | 174/213 [00:18<00:05,  7.40it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  82%|########2 | 175/213 [00:18<00:05,  7.28it/s]
Volume Decoding:  83%|########2 | 176/213 [00:18<00:04,  7.45it/s]
Volume Decoding:  83%|########3 | 177/213 [00:18<00:04,  7.33it/s]
Volume Decoding:  84%|########3 | 178/213 [00:18<00:04,  7.25it/s]
Volume Decoding:  84%|########4 | 179/213 [00:18<00:04,  7.45it/s]
Volume Decoding:  85%|########4 | 180/213 [00:18<00:04,  7.36it/s]
Volume Decoding:  85%|########4 | 181/213 [00:19<00:04,  7.26it/s]
Volume Decoding:  85%|########5 | 182/213 [00:19<00:04,  7.42it/s]
Volume Decoding:  86%|########5 | 183/213 [00:19<00:04,  7.30it/s]
Volume Decoding:  86%|########6 | 184/213 [00:19<00:04,  7.23it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  87%|########6 | 185/213 [00:19<00:03,  7.39it/s]
Volume Decoding:  87%|########7 | 186/213 [00:19<00:03,  7.27it/s]
Volume Decoding:  88%|########7 | 187/213 [00:19<00:03,  7.43it/s]
Volume Decoding:  88%|########8 | 188/213 [00:20<00:03,  7.29it/s]
Volume Decoding:  89%|########8 | 189/213 [00:20<00:03,  7.47it/s]
Volume Decoding:  89%|########9 | 190/213 [00:20<00:03,  7.36it/s]
Volume Decoding:  90%|########9 | 191/213 [00:20<00:03,  7.26it/s]
Volume Decoding:  90%|######### | 192/213 [00:20<00:02,  7.44it/s]
Volume Decoding:  91%|######### | 193/213 [00:20<00:02,  7.34it/s]
Volume Decoding:  91%|#########1| 194/213 [00:20<00:02,  7.23it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  92%|#########1| 195/213 [00:20<00:02,  7.40it/s]
Volume Decoding:  92%|#########2| 196/213 [00:21<00:02,  7.26it/s]
Volume Decoding:  92%|#########2| 197/213 [00:21<00:02,  7.41it/s]
Volume Decoding:  93%|#########2| 198/213 [00:21<00:02,  7.31it/s]
Volume Decoding:  93%|#########3| 199/213 [00:21<00:01,  7.26it/s]
Volume Decoding:  94%|#########3| 200/213 [00:21<00:01,  7.43it/s]
Volume Decoding:  94%|#########4| 201/213 [00:21<00:01,  7.27it/s]
Volume Decoding:  95%|#########4| 202/213 [00:21<00:01,  7.46it/s]
Volume Decoding:  95%|#########5| 203/213 [00:22<00:01,  7.34it/s]
Volume Decoding:  96%|#########5| 204/213 [00:22<00:01,  7.22it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 16:08:27"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  96%|#########6| 205/213 [00:22<00:01,  7.40it/s]
Volume Decoding:  97%|#########6| 206/213 [00:22<00:00,  7.27it/s]
Volume Decoding:  97%|#########7| 207/213 [00:22<00:00,  7.47it/s]
Volume Decoding:  98%|#########7| 208/213 [00:22<00:00,  7.37it/s]
Volume Decoding:  98%|#########8| 209/213 [00:22<00:00,  7.28it/s]
Volume Decoding:  99%|#########8| 210/213 [00:23<00:00,  7.25it/s]
Volume Decoding:  99%|#########9| 211/213 [00:23<00:00,  7.43it/s]
Volume Decoding: 100%|#########9| 212/213 [00:23<00:00,  7.27it/s]
Volume Decoding: 100%|##########| 213/213 [00:23<00:00,  9.10it/s]

[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] 2025-07-09 16:08:35 | INFO | hunyuan3d_api | Processing mesh (faces: 253842)
[HunyaunServer] [STDOUT] 2025-07-09 16:08:37 | INFO | hunyuan3d_api | Reducing faces to 241149
[HunyaunServer] [STDOUT] 2025-07-09 16:08:38 | INFO | hunyuan3d_api | Skipping texture generation for this request
[HunyaunServer] [STDOUT] 2025-07-09 16:08:38 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 2.58 seconds
[HunyaunServer] [STDOUT] 2025-07-09 16:08:38 | INFO | hunyuan3d_api | Generation completed in 64.88 seconds
[HunyaunServer] Generation request sent successfully
info: [Hunyaun Progress] hunyaun: 100% - Generation complete {"service":"user-service","timestamp":"2025-07-09 16:08:38"}
[IPC Handler] Sending status: hunyaun - 100% - Generation complete
[HunyaunServer] Downloading generated model...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] 2025-07-09 16:08:38 | INFO | hunyuan3d_api | Client is downloading a model.
[HunyaunServer] Model saved to: N:\3D AI Studio\output\hunyaun_model_2025-07-09T21-08-38-670Z.glb
info: [Hunyaun Progress] hunyaun_preprocessing: 100% - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-09 16:08:38"}
[IPC Handler] Sending status: hunyaun_preprocessing - 100% - Image Preprocessing: Preparing image for 3D generation
info: [load-file] Received request for: output\hunyaun_model_2025-07-09T21-08-38-670Z.glb {"service":"user-service","timestamp":"2025-07-09 16:08:38"}
info: [load-file] Reading absolute path: N:\3D AI Studio\output\hunyaun_model_2025-07-09T21-08-38-670Z.glb {"service":"user-service","timestamp":"2025-07-09 16:08:38"}
warn: [load-file] Received invalid request for: undefined {"service":"user-service","timestamp":"2025-07-09 16:08:38"}