import React, { useState, useEffect } from 'react';
import { MoreVertical, Download, Save, FileDown, FileUp, Trash2, Edit2, X } from 'lucide-react';
import Masonry from 'react-masonry-css';
import ImageGenerationProgressBar from './ImageGenerationProgressBar';

interface ImageGenerationProps {
  isDarkMode: boolean;
  onSaveAsProject: (imageData: string) => void;
  onProjectSaved?: () => void;
}

const MODEL_OPTIONS = [
  { value: 'flux-dev', label: 'FLUX Dev (High Quality, Advanced Generation)' },
  { value: 'flux-dev-quantized', label: 'FLUX Dev Quantized (4-bit, Memory Efficient, Real-time Preview)' },
  { value: 'sdxl-turbo', label: 'SDXL Turbo (Fast, Good Quality)' },
  { value: 'stable-diffusion-xl-base-1.0', label: 'SDXL Base (High Quality)' },
  { value: 'stable-diffusion-v1-5', label: 'Stable Diffusion v1.5 (Standard Quality)' },
  { value: 'stable-diffusion-2-1', label: 'Stable Diffusion v2.1 (Improved Quality)' },
];

// Local storage keys
const COLLECTIONS_KEY = 'imagegen_collections';
const ACTIVE_COLLECTION_KEY = 'imagegen_active_collection';

interface ImageCollection {
  name: string;
  images: string[];
}

export const ImageGeneration: React.FC<ImageGenerationProps> = ({ isDarkMode, onSaveAsProject, onProjectSaved }) => {
  // State for model selection, prompt, settings, generated images, preview, etc.
  const [model, setModel] = useState('flux-dev');
  const [useRefiner, setUseRefiner] = useState(false);  // SDXL Refiner enhancement option
  const [refinerSteps, setRefinerSteps] = useState(10);  // Optimal refiner steps
  const [prompt, setPrompt] = useState('');
  const [steps, setSteps] = useState(20);  // FLUX Dev optimal steps
  const [width, setWidth] = useState(1024);  // FLUX Dev optimal resolution
  const [height, setHeight] = useState(1024);  // FLUX Dev optimal resolution
  const [guidanceScale, setGuidanceScale] = useState(3.5);  // FLUX Dev optimal guidance scale
  const [sampler, setSampler] = useState('euler_a');  // FLUX Dev optimal scheduler
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [progressText, setProgressText] = useState<string>('');
  const [livePreview, setLivePreview] = useState<string | null>(null);
  const [currentStage, setCurrentStage] = useState<string>('');
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [totalSteps, setTotalSteps] = useState<number>(0);
  const [stageProgress, setStageProgress] = useState<number>(0);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Collections state
  const [collections, setCollections] = useState<ImageCollection[]>([]);
  const [activeCollection, setActiveCollection] = useState<string>('');

  // Modal state for creating a new collection
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [renameCollectionName, setRenameCollectionName] = useState('');

  // Dropdown state for collection actions
  const [showActionsDropdown, setShowActionsDropdown] = useState(false);

  // Add this state for thumbnail size
  const [columns, setColumns] = useState(2); // default 2 columns
  const [windowHeight, setWindowHeight] = useState(window.innerHeight);

  // Load collections from localStorage on mount
  useEffect(() => {
    const storedCollections = localStorage.getItem(COLLECTIONS_KEY);
    const storedActive = localStorage.getItem(ACTIVE_COLLECTION_KEY);
    let parsedCollections: ImageCollection[] = [];
    if (storedCollections) {
      try {
        parsedCollections = JSON.parse(storedCollections);
      } catch {}
    }
    if (parsedCollections.length === 0) {
      parsedCollections = [{ name: 'Default', images: [] }];
    }
    setCollections(parsedCollections);
    setActiveCollection(storedActive && parsedCollections.find(c => c.name === storedActive) ? storedActive : parsedCollections[0].name);
  }, []);

  // Persist collections and active collection to localStorage
  useEffect(() => {
    localStorage.setItem(COLLECTIONS_KEY, JSON.stringify(collections));
  }, [collections]);
  useEffect(() => {
    localStorage.setItem(ACTIVE_COLLECTION_KEY, activeCollection);
  }, [activeCollection]);

  useEffect(() => {
    const handleResize = () => setWindowHeight(window.innerHeight);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Get images for the active collection
  const generatedImages = collections.find(c => c.name === activeCollection)?.images || [];

  // Add image to active collection
  const addImageToActiveCollection = (img: string) => {
    setCollections(prev => prev.map(c => c.name === activeCollection ? { ...c, images: [img, ...c.images] } : c));
  };

  // Clear all images in active collection
  const clearActiveCollection = () => {
    if (window.confirm('Clear all images in this collection?')) {
      setCollections(prev => prev.map(c => c.name === activeCollection ? { ...c, images: [] } : c));
    }
  };

  // Create a new collection (modal version)
  const handleCreateCollection = () => {
    setShowCreateModal(true);
    setNewCollectionName('');
  };
  const confirmCreateCollection = () => {
    const name = newCollectionName.trim();
    if (!name) return;
    if (collections.some(c => c.name === name)) {
      alert('A collection with that name already exists.');
      return;
    }
    setCollections(prev => [...prev, { name, images: [] }]);
    setActiveCollection(name);
    setShowCreateModal(false);
    setNewCollectionName('');
  };
  const cancelCreateCollection = () => {
    setShowCreateModal(false);
    setNewCollectionName('');
  };

  // Delete the current collection
  const deleteActiveCollection = () => {
    if (collections.length === 1) {
      alert('You must have at least one collection.');
      return;
    }
    if (window.confirm(`Delete collection "${activeCollection}"? This cannot be undone.`)) {
      const idx = collections.findIndex(c => c.name === activeCollection);
      const newCollections = collections.filter(c => c.name !== activeCollection);
      setCollections(newCollections);
      setActiveCollection(newCollections[Math.max(0, idx - 1)].name);
    }
  };

  // Export the current collection as JSON
  const exportActiveCollection = () => {
    const collection = collections.find(c => c.name === activeCollection);
    if (!collection) return;
    const dataStr = JSON.stringify(collection, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${collection.name.replace(/\s+/g, '_')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Import a collection from JSON
  const importCollection = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const imported = JSON.parse(event.target?.result as string);
        if (!imported.name || !Array.isArray(imported.images)) {
          alert('Invalid collection format.');
          return;
        }
        let newName = imported.name;
        let suffix = 1;
        while (collections.some(c => c.name === newName)) {
          newName = `${imported.name} (${suffix++})`;
        }
        setCollections(prev => [...prev, { name: newName, images: imported.images }]);
        setActiveCollection(newName);
      } catch {
        alert('Failed to import collection.');
      }
    };
    reader.readAsText(file);
    // Reset the input so the same file can be imported again if needed
    e.target.value = '';
  };

  // Rename the current collection
  const renameActiveCollection = () => {
    setRenameCollectionName(activeCollection);
    setShowRenameModal(true);
    setShowActionsDropdown(false);
  };

  const confirmRenameCollection = () => {
    const newName = renameCollectionName.trim();
    if (!newName || newName === activeCollection) return;
    if (collections.some(c => c.name === newName)) {
      alert('A collection with that name already exists.');
      return;
    }
    setCollections(prev => prev.map(c => c.name === activeCollection ? { ...c, name: newName } : c));
    setActiveCollection(newName);
    setShowRenameModal(false);
  };

  const cancelRenameCollection = () => {
    setShowRenameModal(false);
    setRenameCollectionName('');
  };

  // Delete an individual image from the collection
  const deleteImage = (index: number) => {
    if (window.confirm('Delete this image?')) {
      setCollections(prev => prev.map(c => 
        c.name === activeCollection 
          ? { ...c, images: c.images.filter((_, i) => i !== index) }
          : c
      ));
    }
  };

  // Handle image generation using Electron IPC
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      alert('Please enter a prompt to generate an image.');
      return;
    }

    setIsGenerating(true);
    setPreviewImage(null);
    setLivePreview(null);
    setProgress(0);
    setProgressText('Starting image generation...');
    setCurrentStage('');
    setCurrentStep(0);
    setTotalSteps(0);
    setStageProgress(0);
    
    let removeProgressListener: (() => void) | null = null;
    
    try {
      // Set up enhanced progress listener with live preview
      removeProgressListener = (window as any).electronAPI.onImageGenerationProgress((data: any) => {
        // Update basic progress
        if (data.progress !== undefined) setProgress(data.progress);
        if (data.status) setProgressText(data.status);
        
        // Update enhanced progress tracking
        if (data.stage) setCurrentStage(data.stage);
        if (data.step !== undefined) setCurrentStep(data.step);
        if (data.total !== undefined) setTotalSteps(data.total);
        if (data.stage_progress !== undefined) setStageProgress(data.stage_progress);
        if (data.message) setProgressText(data.message);
        
        // Handle live preview during generation
        if (data.preview_base64) {
          const previewSrc = `data:image/png;base64,${data.preview_base64}`;
          setLivePreview(previewSrc);
        }
        
        // Handle final generated image
        if (data.image_base64) {
          const imgSrc = `data:image/png;base64,${data.image_base64}`;
          setPreviewImage(imgSrc);
          setLivePreview(null); // Clear live preview when final image is ready
          addImageToActiveCollection(imgSrc);
        }
        
        // Handle errors
        if (data.error) {
          console.error('Image generation error:', data.error);
          alert(`Generation failed: ${data.error}`);
          setProgress(0);
          setProgressText('Generation failed');
          setLivePreview(null);
          setCurrentStage('');
        }
      });

      const result = await (window as any).electronAPI.generateImageStream({
        prompt: prompt.trim(),
        model,
        settings: {
          num_inference_steps: steps,
          guidance_scale: guidanceScale,
          width,
          height,
          scheduler: sampler !== 'default' ? sampler : undefined,
          // SDXL Refiner settings
          use_refiner: useRefiner && model === 'stable-diffusion-xl-base-1.0',
          refiner_steps: refinerSteps,
          // Add seed for reproducibility if needed
          seed: Math.floor(Math.random() * 2147483647)
        }
      });

      if (result.success) {
        // Final image should already be set by progress callback
        setProgress(100);
        setProgressText('Generation complete!');
      } else {
        throw new Error(result.error || 'Image generation failed');
      }
    } catch (err: any) {
      console.error('Image generation error:', err);
      alert(`Failed to generate image: ${err.message || 'Unknown error'}`);
      setProgress(0);
      setProgressText('Generation failed');
    } finally {
      setIsGenerating(false);
      setLivePreview(null);
      setCurrentStage('');
      setCurrentStep(0);
      setTotalSteps(0);
      setStageProgress(0);
      if (removeProgressListener) {
        removeProgressListener();
      }
    }
  };

  const handleSaveAsProject = async (imageData: string) => {
    setShowSaveModal(true);
  };

  const handleSaveProject = async () => {
    if (!previewImage || !projectName.trim()) return;
    
    setIsSaving(true);
    try {
      // Extract base64 data from the data URL
      const imageBase64 = previewImage.split(',')[1];
      
      const saveResult = await (window as any).electronAPI.createProjectFromImage({
        name: projectName.trim(),
        type: 'image-generation',
        image_base64: imageBase64,
        prompt,
        model,
        settings: {
          text_to_image_model: model,
          image_size: width, // Since width and height are the same
          guidance_scale: guidanceScale,
          num_inference_steps: steps,
          width,
          height,
          seed: Math.floor(Math.random() * 2147483647) // Random seed if not specified
        }
      });

      if (!saveResult.success) {
        throw new Error(saveResult.error || 'Failed to save project');
      }

      console.log('Project saved:', saveResult);
      
      // Reset form and close modal
      setProjectName('');
      setShowSaveModal(false);
      
      // Refresh project gallery if callback exists
      if (onProjectSaved) {
        onProjectSaved();
      }
    } catch (error) {
      console.error('Error saving project:', error);
      alert(error instanceof Error ? error.message : 'Failed to save project. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="h-full flex flex-col lg:flex-row gap-6 max-w-full mx-auto">
      {/* Left Panel: Model selection and generation settings */}
      <div className="w-full lg:w-80 lg:flex-shrink-0 space-y-6">
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg`}>
          <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Image Generation Settings</h2>
          {/* Model Selection */}
          <label className={`block mb-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Model</label>
          <select
            value={model}
            onChange={e => {
              setModel(e.target.value);
              // Reset refiner if not SDXL Base
              if (e.target.value !== 'stable-diffusion-xl-base-1.0') {
                setUseRefiner(false);
              }
              // Set research-based optimal defaults for each model
              if (e.target.value === 'sdxl-turbo') {
                // SDXL Turbo: Fixed for quality issues found in research
                setSteps(4);  // 1-4 steps work best, 4 is more stable than 1
                setGuidanceScale(0.0);  // CRITICAL: Must be 0.0 to avoid artifacts
                setSampler('euler_a');  // Better than default for SDXL Turbo
                setWidth(512);  // SDXL Turbo is optimized for 512x512, NOT 1024x1024!
                setHeight(512);  // Research shows 512x512 gives best quality for SDXL Turbo
              } else if (e.target.value === 'flux-schnell') {
                // FLUX Schnell: Optimized for exactly 4 steps at 512x512 on RTX 3060
                setSteps(4);  // Designed for exactly 4 steps
                setGuidanceScale(0.0);  // FLUX doesn't use CFG guidance
                setSampler('euler_a');  // Recommended scheduler for FLUX
                setWidth(512);  // Force 512x512 to prevent VRAM crashes on RTX 3060
                setHeight(512);
              } else if (e.target.value === 'flux-dev-quantized') {
                // FLUX Dev Quantized: High quality with memory efficiency for RTX 3060
                setSteps(25);  // FLUX Dev needs more steps than Schnell for quality
                setGuidanceScale(3.5);  // FLUX Dev uses guidance scale
                setSampler('euler_a');  // Recommended scheduler for FLUX
                setWidth(1024);  // 1024x1024 default for quantized FLUX
                setHeight(1024);
              } else if (e.target.value === 'flux-dev') {
                // FLUX Dev: High quality settings with optimal resolution
                setSteps(20);  // FLUX Dev works well with 20-50 steps
                setGuidanceScale(3.5);  // FLUX Dev uses guidance scale for quality
                setSampler('euler_a');  // Recommended scheduler for FLUX
                setWidth(1024);  // FLUX Dev optimal resolution
                setHeight(1024);  // FLUX Dev optimal resolution
              } else if (e.target.value === 'stable-diffusion-xl-base-1.0') {
                // SDXL Base: High quality settings (can use refiner)
                setSteps(30);  // 20+ steps recommended for quality
                setGuidanceScale(7.5);  // Standard CFG for SDXL Base
                setSampler('dpm');  // DPM++ excellent for SDXL
                setWidth(1024);
                setHeight(1024);
              } else {
                // SD v1.5, v2.1: Traditional settings
                setSteps(25);
                setGuidanceScale(7.5);
                setSampler('euler_a');  // Reliable for SD 1.5/2.1
                setWidth(512);  // Native resolution for SD 1.5/2.1
                setHeight(512);
              }
            }}
            className={`w-full px-3 py-2 rounded-lg border mb-4 ${
              isDarkMode
                ? 'bg-gray-800 border-gray-600 text-white focus:border-blue-400'
                : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
            disabled={isGenerating}
          >
            {MODEL_OPTIONS.map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>

          {/* SDXL Refiner Option */}
          {model === 'stable-diffusion-xl-base-1.0' && (
            <div className={`mb-4 p-3 rounded-lg border ${isDarkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-blue-50 border-blue-200'}`}>
              <div className="flex items-center justify-between mb-2">
                <label className={`flex items-center text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  <input
                    type="checkbox"
                    checked={useRefiner}
                    onChange={e => setUseRefiner(e.target.checked)}
                    className="mr-2"
                    disabled={isGenerating}
                  />
                  Use SDXL Refiner Enhancement
                </label>
              </div>
              {useRefiner && (
                <>
                  <label className={`block mb-1 text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Refiner Steps: {refinerSteps}
                  </label>
                  <input
                    type="range"
                    min={5}
                    max={20}
                    value={refinerSteps}
                    onChange={e => setRefinerSteps(Number(e.target.value))}
                    className="w-full mb-2"
                    disabled={isGenerating}
                  />
                </>
              )}
              <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {useRefiner 
                  ? `✅ The refiner will enhance image quality after base generation with ${refinerSteps} additional steps.`
                  : '💡 The SDXL Refiner can enhance image quality and detail after base generation.'
                }
              </p>
            </div>
          )}

          {/* Prompt Input */}
          <label className={`block mb-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Prompt</label>
          <textarea
            value={prompt}
            onChange={e => setPrompt(e.target.value)}
            className={`w-full h-24 p-3 rounded-lg border resize-none mb-4 ${
              isDarkMode
                ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
            placeholder="Describe the image you want to generate..."
            disabled={isGenerating}
          />

          {/* Scheduler Selection */}
          <label className={`block mb-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Scheduler
            {(model === 'flux-dev' || model === 'flux-dev-quantized') && <span className={`text-xs ml-1 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>(Recommended: normal)</span>}
          </label>
          <select
            value={sampler}
            onChange={e => setSampler(e.target.value)}
            className={`w-full px-3 py-2 rounded-lg border mb-4 ${
              isDarkMode
                ? 'bg-gray-800 border-gray-600 text-white focus:border-blue-400'
                : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
            disabled={isGenerating}
          >
            {(model === 'flux-dev' || model === 'flux-dev-quantized') ? (
              <>
                <option value="normal">normal (default)</option>
                <option value="sgm_uniform">sgm_uniform</option>
                <option value="simple">simple</option>
                <option value="beta">beta</option>
                <option value="ddim_uniform">ddim_uniform</option>
                <option value="ddm_uniform">ddm_uniform</option>
                <option value="sgm_discrete">sgm_discrete</option>
              </>
            ) : (
              <>
                <option value="default">Default</option>
                <option value="euler">Euler</option>
                <option value="euler_a">Euler A</option>
                <option value="dpm">DPM Solver++</option>
                <option value="dpm_single">DPM Single</option>
                <option value="ddim">DDIM</option>
                <option value="ddpm">DDPM</option>
                <option value="lms">LMS</option>
                <option value="pndm">PNDM</option>
              </>
            )}
          </select>

          {/* Steps Slider */}
          <label className={`block mb-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Inference Steps: {steps}</label>
          <input
            type="range"
            min={1}
            max={model === 'sdxl-turbo' ? 4 : (model === 'flux-dev' ? 50 : 50)}
            value={steps}
            onChange={e => setSteps(Number(e.target.value))}
            className="w-full mb-4"
            disabled={isGenerating}
          />

          {/* Guidance Scale (CFG) Slider */}
          <label className={`block mb-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Guidance Scale (CFG): {guidanceScale}
            {model === 'sdxl-turbo' && 
              <span className={`text-xs ml-2 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>
                (Not used by SDXL Turbo)
              </span>
            }
            {model === 'flux-dev' &&
              <span className={`text-xs ml-2 ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                (FLUX Dev uses guidance scale for quality)
              </span>
            }
          </label>
          <input
            type="range"
            min={0.0}
            max={20.0}
            step={0.1}
            value={guidanceScale}
            onChange={e => setGuidanceScale(Number(e.target.value))}
            className={`w-full mb-4 ${model === 'sdxl-turbo' ? 'opacity-50' : ''}`}
            disabled={isGenerating}
          />
          {model === 'sdxl-turbo' && 
            <p className={`text-xs mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              SDXL Turbo uses distillation and doesn't require guidance scale (forced to 0.0)
            </p>
          }
          {model === 'flux-dev' &&
            <p className={`text-xs mb-4 ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
              FLUX Dev supports guidance scale for enhanced quality and uses your custom VAE.
            </p>
          }

          {/* Width/Height Sliders */}
          <label className={`block mb-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Width: {width}px</label>
          <input
            type="range"
            min={256}
            max={1900}
            step={64}
            value={width}
            onChange={e => setWidth(Number(e.target.value))}
            className="w-full mb-4"
            disabled={isGenerating}
          />
          <label className={`block mb-2 text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Height: {height}px</label>
          <input
            type="range"
            min={256}
            max={1900}
            step={64}
            value={height}
            onChange={e => setHeight(Number(e.target.value))}
            className="w-full mb-4"
            disabled={isGenerating}
          />
          {model === 'sdxl-turbo' && (width !== 512 || height !== 512) && 
            <p className={`text-xs mb-4 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>
              ⚠️ SDXL Turbo works best at 512x512 resolution. Other sizes may cause image quality issues.
            </p>
          }
          {model === 'flux-dev' && (width !== 1024 || height !== 1024) && 
            <p className={`text-xs mb-4 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>
              ⚠️ FLUX Dev works best at 1024x1024 resolution for optimal quality.
            </p>
          }

          {/* Generate Button */}
          <button
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.trim()}
            className={`w-full py-2 rounded-lg font-semibold transition-colors ${
              isGenerating
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isGenerating ? 'Generating...' : 'Generate'}
          </button>
        </div>
      </div>
      {/* Center Panel: Enhanced image preview with live generation tracking */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className={`${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'} rounded-lg p-4 shadow-lg w-full h-full flex items-center justify-center relative`} style={{ minHeight: 0 }}>
          {/* Final generated image */}
          {previewImage ? (
            <div className="w-full h-full flex flex-col items-center justify-center relative">
              <img src={previewImage} alt="Generated Image" className="rounded" style={{ maxWidth: '100%', maxHeight: '100%', padding: 8, boxSizing: 'border-box', display: 'block', margin: 'auto' }} />
              <div className={`absolute bottom-2 right-2 px-2 py-1 rounded text-xs ${isDarkMode ? 'bg-green-800/80 text-green-200' : 'bg-green-600/80 text-white'}`}>
                ✅ Complete
              </div>
            </div>
          ) : livePreview ? (
            /* Live preview during generation with enhanced visual feedback */
            <div className="w-full h-full flex flex-col items-center justify-center relative">
              <img
                src={livePreview}
                alt="Live Preview"
                className="rounded transition-all duration-300"
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  padding: 8,
                  boxSizing: 'border-box',
                  display: 'block',
                  margin: 'auto',
                  filter: 'brightness(0.9) contrast(1.1)' // Slightly enhance preview visibility
                }}
              />

              {/* Live preview indicators */}
              <div className={`absolute top-2 left-2 px-2 py-1 rounded text-xs animate-pulse ${isDarkMode ? 'bg-blue-800/90 text-blue-200' : 'bg-blue-600/90 text-white'}`}>
                🔄 Live Preview
              </div>

              {/* Step counter */}
              {currentStep > 0 && totalSteps > 0 && (
                <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs ${isDarkMode ? 'bg-purple-800/90 text-purple-200' : 'bg-purple-600/90 text-white'}`}>
                  Step {currentStep}/{totalSteps}
                </div>
              )}

              {/* Progress indicator */}
              <div className={`absolute bottom-2 left-2 right-2 h-1 rounded-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`}>
                <div
                  className={`h-full rounded-full transition-all duration-300 ${isDarkMode ? 'bg-blue-500' : 'bg-blue-600'}`}
                  style={{ width: `${stageProgress}%` }}
                />
              </div>
            </div>
          ) : (
            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {isGenerating ? 'Generating image...' : 'Image preview will appear here'}
            </span>
          )}
        </div>
        
        {/* Enhanced progress tracking during generation */}
        {isGenerating && (
          <div className="w-full max-w-xl mt-4">
            <ImageGenerationProgressBar
              isDarkMode={isDarkMode}
              currentStage={currentStage}
              progress={progress}
              progressText={progressText}
              currentStep={currentStep}
              totalSteps={totalSteps}
              stageProgress={stageProgress}
            />

            {/* Live preview indicator */}
            {livePreview && (
              <div className={`text-center text-xs mt-3 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                🔄 Live preview updating... Final image will appear when complete
              </div>
            )}

            {/* FLUX Dev note about previews */}
            {model === 'flux-dev' && !livePreview && (
              <div className={`text-center text-xs mt-3 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>
                ℹ️ FLUX Dev doesn't support real-time previews - final image will appear when generation is complete
              </div>
            )}
          </div>
        )}
        
        <button
          onClick={() => previewImage && handleSaveAsProject(previewImage)}
          disabled={!previewImage}
          className={`mt-4 px-4 py-2 rounded-lg font-semibold transition-colors ${
            previewImage
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-400 text-gray-200 cursor-not-allowed'
          }`}
          type="button"
        >
          Save as Project
        </button>
      </div>
      {/* Right Panel: Thumbnails of generated images and collections */}
      <div className="w-full lg:w-80 lg:flex-shrink-0 space-y-6">
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg`}>
          {/* Collections Header */}
          <div className="mb-4">
            <label className={`block text-md font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Collections</label>
            <div className="flex gap-2">
              <select
                value={activeCollection}
                onChange={e => setActiveCollection(e.target.value)}
                className={`flex-1 px-2 py-1 rounded ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-700'}`}
              >
                {collections.map(c => (
                  <option key={c.name} value={c.name}>{c.name}</option>
                ))}
              </select>
              <button
                onClick={handleCreateCollection}
                className={`px-2 py-1 rounded text-xs font-semibold ${isDarkMode ? 'bg-blue-700 text-white' : 'bg-blue-100 text-blue-700'}`}
                title="Create new collection"
                type="button"
              >
                + New
              </button>
            </div>
          </div>

          {/* Collection Actions Dropdown */}
          <div className="relative mb-4">
            <button
              onClick={() => setShowActionsDropdown(!showActionsDropdown)}
              className={`w-full px-3 py-2 rounded-lg flex items-center justify-between ${
                isDarkMode 
                  ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
              type="button"
            >
              <span className="text-sm font-medium">Collection Actions</span>
              <MoreVertical className="w-4 h-4" />
            </button>

            {showActionsDropdown && (
              <div className={`absolute z-10 w-full mt-1 rounded-lg shadow-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <div className="py-1">
                  <button
                    onClick={() => {
                      renameActiveCollection();
                      setShowActionsDropdown(false);
                    }}
                    className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-gray-300' 
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                    type="button"
                  >
                    <Edit2 className="w-4 h-4" />
                    Rename Collection
                  </button>
                  <button
                    onClick={() => {
                      deleteActiveCollection();
                      setShowActionsDropdown(false);
                    }}
                    className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-red-400' 
                        : 'hover:bg-gray-100 text-red-600'
                    }`}
                    type="button"
                    disabled={collections.length === 1}
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete Collection
                  </button>
                  <button
                    onClick={() => {
                      clearActiveCollection();
                      setShowActionsDropdown(false);
                    }}
                    className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-gray-300' 
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                    type="button"
                    disabled={generatedImages.length === 0}
                  >
                    <X className="w-4 h-4" />
                    Clear Collection
                  </button>
                  <button
                    onClick={() => {
                      exportActiveCollection();
                      setShowActionsDropdown(false);
                    }}
                    className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-gray-300' 
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                    type="button"
                  >
                    <FileDown className="w-4 h-4" />
                    Export Collection
                  </button>
                  <label
                    className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 cursor-pointer ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-gray-300' 
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    <FileUp className="w-4 h-4" />
                    Import Collection
                    <input
                      type="file"
                      accept="application/json"
                      onChange={(e) => {
                        importCollection(e);
                        setShowActionsDropdown(false);
                      }}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>
            )}
          </div>

          <div className="flex-1 h-full pr-2" style={{ height: `calc(100vh - 350px)`, overflowY: 'auto' }}>
            {/* Thumbnails Section Controls */}
            <div className="flex items-center gap-3 mb-2">
              <label className={`text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Columns</label>
              <input
                type="range"
                min={1}
                max={6}
                step={1}
                value={columns}
                onChange={e => setColumns(Number(e.target.value))}
                className="w-32"
              />
              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{columns}</span>
            </div>

            {/* Thumbnails Section */}
            <Masonry
              breakpointCols={{ default: columns }}
              className="masonry-grid"
              columnClassName="masonry-grid_column"
            >
              {generatedImages.map((img, idx) => (
                <div key={idx} className="relative group">
                  <img
                    src={img}
                    alt={`Generated ${idx}`}
                    className="rounded cursor-pointer border border-gray-300 transition-all duration-200 w-full h-auto"
                    onClick={() => setPreviewImage(img)}
                  />
                  <div
                    className={
                      `absolute bottom-1 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity z-10 ` +
                      ((idx % columns === 0)
                        ? 'left-1 right-auto flex-row'
                        : 'right-1 left-auto flex-row-reverse')
                    }
                  >
                    <button
                      onClick={() => handleSaveAsProject(img)}
                      className="px-2 py-1 text-xs rounded bg-blue-600 text-white"
                      title="Save as Project"
                      type="button"
                    >
                      <Save className="w-3 h-3" />
                    </button>
                    <button
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = img;
                        link.download = `generated-image-${idx + 1}.png`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                      className="px-2 py-1 text-xs rounded bg-green-600 text-white"
                      title="Download Image"
                      type="button"
                    >
                      <Download className="w-3 h-3" />
                    </button>
                    <button
                      onClick={() => deleteImage(idx)}
                      className="px-2 py-1 text-xs rounded bg-red-600 text-white"
                      title="Delete Image"
                      type="button"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              ))}
            </Masonry>
          </div>
        </div>
      </div>
      {/* Modal for creating a new collection */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className={`rounded-lg p-6 shadow-lg w-full max-w-xs ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}`}
               style={{ minWidth: 300 }}>
            <h2 className="text-lg font-semibold mb-4">Create New Collection</h2>
            <input
              type="text"
              value={newCollectionName}
              onChange={e => setNewCollectionName(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border mb-4 ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-100 border-gray-300 text-gray-900'}`}
              placeholder="Collection name"
              autoFocus
              onKeyDown={e => { if (e.key === 'Enter') confirmCreateCollection(); }}
            />
            <div className="flex gap-2 justify-end">
              <button
                onClick={cancelCreateCollection}
                className={`px-3 py-1 rounded ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-700'}`}
                type="button"
              >
                Cancel
              </button>
              <button
                onClick={confirmCreateCollection}
                className={`px-3 py-1 rounded ${isDarkMode ? 'bg-blue-700 text-white' : 'bg-blue-600 text-white'}`}
                type="button"
                disabled={!newCollectionName.trim()}
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Modal for renaming collection */}
      {showRenameModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className={`rounded-lg p-6 shadow-lg w-full max-w-xs ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}`}
               style={{ minWidth: 300 }}>
            <h2 className="text-lg font-semibold mb-4">Rename Collection</h2>
            <input
              type="text"
              value={renameCollectionName}
              onChange={e => setRenameCollectionName(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border mb-4 ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-100 border-gray-300 text-gray-900'}`}
              placeholder="New collection name"
              autoFocus
              onKeyDown={e => { if (e.key === 'Enter') confirmRenameCollection(); }}
            />
            <div className="flex gap-2 justify-end">
              <button
                onClick={cancelRenameCollection}
                className={`px-3 py-1 rounded ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-700'}`}
                type="button"
              >
                Cancel
              </button>
              <button
                onClick={confirmRenameCollection}
                className={`px-3 py-1 rounded ${isDarkMode ? 'bg-blue-700 text-white' : 'bg-blue-600 text-white'}`}
                type="button"
                disabled={!renameCollectionName.trim()}
              >
                Rename
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Save Project Modal */}
      {showSaveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-xl max-w-md w-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Save as Project
            </h3>
            <div className="mb-4">
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Project Name
              </label>
              <input
                type="text"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                className={`w-full px-3 py-2 rounded-lg border ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter project name"
                autoFocus
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowSaveModal(false);
                  setProjectName('');
                }}
                className={`px-4 py-2 rounded-lg font-medium ${
                  isDarkMode 
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                disabled={isSaving}
              >
                Cancel
              </button>
              <button
                onClick={handleSaveProject}
                disabled={!projectName.trim() || isSaving}
                className={`px-4 py-2 rounded-lg font-medium ${
                  !projectName.trim() || isSaving
                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isSaving ? 'Saving...' : 'Save Project'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGeneration; 