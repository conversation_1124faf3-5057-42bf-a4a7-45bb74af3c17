PS N:\3D AI Studio> npm start

> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
[Trellis Server] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",      
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-09 17:14:44"}    
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 17:14:44"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 17:14:44"}
[Trellis Server] Found Python processes, killing them...
[Trellis Server] Processes found: python.exe                   21300 Console                    1 22,498,768 K
[Trellis Server] Successfully killed Python processes
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-09 17:14:48"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 17:14:49"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 17:14:49"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-09 17:14:49"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-09 17:14:49"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-09 17:14:49"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-09 17:14:50"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-09 17:14:51"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-09 17:14:51"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 17:14:51"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-09 17:14:52"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-09 17:14:52"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-09 17:14:52"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-09 17:14:55"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-09 17:14:55"}
info: [upload-file] Received ΓÇô filename: 02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern.png, size: 954053 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-09 17:15:00"}
info: Buffer received (bytes): 954053 {"service":"user-service","timestamp":"2025-07-09 17:15:00"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\63118b32-69d8-418c-878d-************\02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern.png {"service":"user-service","timestamp":"2025-07-09 17:15:00"}
info: Starting background removal for N:\3D AI Studio\uploads\63118b32-69d8-418c-878d-************\02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern.png... {"service":"user-service","timestamp":"2025-07-09 17:15:00"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\63118b32-69d8-418c-878d-************\02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern.png N:\3D AI Studio\uploads\63118b32-69d8-418c-878d-************\02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern_processed.png {"service":"user-service","timestamp":"2025-07-09 17:15:00"}
info: [load-file] Received request for: uploads/63118b32-69d8-418c-878d-************/02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern_processed.png {"service":"user-service","timestamp":"2025-07-09 17:15:07"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\63118b32-69d8-418c-878d-************\02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern_processed.png {"service":"user-service","timestamp":"2025-07-09 17:15:07"}
info: IPC: run-pipeline called for: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 17:15:15"}
info: runPipeline request: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 17:15:15"}
info: runPipeline: Registered pipelines at call time: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 17:15:15"}
info: Checking dependencies for Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 17:15:15"}
warn: Python dependencies not satisfied for Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 17:15:15"}
[IPC Handler] Sending status: hunyaun - 25% - Launching Hunyaun server and generating 3D model...
[HunyaunServer] generate3DModel called with imagePath: uploads/63118b32-69d8-418c-878d-************/02f234e7-140d-4775-9563-067b4e5862c4.png_Red_Lantern_processed.png
[HunyaunServer] Settings received: {
  ss_steps: 12,
  ss_cfg_strength: 7.5,
  slat_steps: 12,
  slat_cfg_strength: 3,
  randomize_seed: true,
  seed: 216583,
  simplify: 0.95,
  texture_size: 1024,
  enable_lighting_optimizer: true,
  octree_resolution: 128,
  num_inference_steps: 5,
  guidance_scale: 5,
  enable_texture: true,
  face_count: 40000
}
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] isHunyaunRunning() returned: false
[HunyaunServer] [Hunyaun Progress] Stage tracking reset for new generation      
[HunyaunServer] Server not running, starting server...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] startHunyaunServer() called successfully
[HunyaunServer] Waiting for Hunyaun server to be ready...
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] Creating virtual environment using portable Python...
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe" -m virtualenv "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] sitecustomize.py applied
[HunyaunServer] [STDOUT]         1 file(s) copied.
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 17:15:24"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (10 attempts): ECONNREFUSED
[HunyaunServer] Connection check #10
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-09 17:15:44,302 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 17:15:44,303 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[HunyaunServer] [STDOUT] 2025-07-09 17:15:44 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (20 attempts): ECONNREFUSED
[HunyaunServer] Connection check #20
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

[HunyaunServer] [STDERR] 2025-07-09 17:15:56,977 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[HunyaunServer] [STDOUT] 2025-07-09 17:15:56 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 17:16:04"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (30 attempts): ECONNREFUSED
[HunyaunServer] Connection check #30
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (40 attempts): ECONNREFUSED
[HunyaunServer] Connection check #40
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 2153.13it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 8467.30it/s]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
Loading pipeline components...:  17%|#6        | 1/6 [00:03<00:16,  3.38s/it]
Loading pipeline components...:  67%|######6   | 4/6 [00:03<00:01,  1.48it/s]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...: 100%|##########| 6/6 [00:04<00:00,  1.43it/s]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[HunyaunServer] Server not running - connection failed (50 attempts): ECONNREFUSED
[HunyaunServer] Connection check #50
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 17:16:55"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  17%|#6        | 1/6 [00:17<01:29, 17.86s/it]
Loading pipeline components...:  67%|######6   | 4/6 [00:18<00:06,  3.43s/it]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...: 100%|##########| 6/6 [00:22<00:00,  3.75s/it]

[HunyaunServer] [STDOUT] 2025-07-09 17:17:08 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] ==================================================     
[HunyaunServer] [STDOUT] Server is active and listening on 127.0.0.1:7960       
[HunyaunServer] Server is running - connection successful
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] Mapped API settings: {
  guidance_scale: 5,
  num_inference_steps: 30,
  octree_resolution: 384,
  texture_size: 1024,
  mesh_simplify_ratio: 0.1,
  num_chunks: 40,
  apply_texture: true
}
[HunyaunServer] Final request data: {
  single_multi_img_input: [ '[IMAGE_DATA_OMITTED]' ],
  seed: 795764,
  guidance_scale: 5,
  num_inference_steps: 30,
  octree_resolution: 384,
  num_chunks: 40,
  mesh_simplify_ratio: 0.1,
  texture_size: 1024,
  apply_texture: true,
  output_format: 'glb'
}
[HunyaunServer] Sending generation request to Hunyaun server...
[HunyaunServer] [STDOUT] ==================================================
[HunyaunServer] [STDOUT] 2025-07-09 17:17:09 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] 2025-07-09 17:17:09 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
[HunyaunServer] [STDOUT] 2025-07-09 17:17:11 | INFO | hunyuan3d_api | Shape generation model moved to cuda
Diffusion Sampling::   0%|          | 0/30 [00:00<?, ?it/s]
Diffusion Sampling::   3%|3         | 1/30 [00:01<00:33,  1.16s/it]
Diffusion Sampling::   7%|6         | 2/30 [00:01<00:19,  1.47it/s]
Diffusion Sampling::  10%|#         | 3/30 [00:02<00:22,  1.20it/s]
Diffusion Sampling::  13%|#3        | 4/30 [00:03<00:23,  1.11it/s]
Diffusion Sampling::  17%|#6        | 5/30 [00:04<00:23,  1.06it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:17:19"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  20%|##        | 6/30 [00:05<00:23,  1.03it/s]
Diffusion Sampling::  23%|##3       | 7/30 [00:06<00:22,  1.02it/s]
Diffusion Sampling::  27%|##6       | 8/30 [00:07<00:21,  1.01it/s]
Diffusion Sampling::  30%|###       | 9/30 [00:08<00:20,  1.00it/s]
Diffusion Sampling::  33%|###3      | 10/30 [00:09<00:20,  1.00s/it]
Diffusion Sampling::  37%|###6      | 11/30 [00:10<00:19,  1.01s/it]
Diffusion Sampling::  40%|####      | 12/30 [00:11<00:18,  1.01s/it]
Diffusion Sampling::  43%|####3     | 13/30 [00:12<00:17,  1.01s/it]
Diffusion Sampling::  47%|####6     | 14/30 [00:13<00:16,  1.01s/it]
Diffusion Sampling::  50%|#####     | 15/30 [00:14<00:15,  1.01s/it]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  53%|#####3    | 16/30 [00:15<00:14,  1.01s/it]
Diffusion Sampling::  57%|#####6    | 17/30 [00:16<00:13,  1.01s/it]
Diffusion Sampling::  60%|######    | 18/30 [00:17<00:12,  1.01s/it]
Diffusion Sampling::  63%|######3   | 19/30 [00:18<00:11,  1.01s/it]
Diffusion Sampling::  67%|######6   | 20/30 [00:19<00:10,  1.01s/it]
Diffusion Sampling::  70%|#######   | 21/30 [00:20<00:09,  1.01s/it]
Diffusion Sampling::  73%|#######3  | 22/30 [00:21<00:08,  1.01s/it]
Diffusion Sampling::  77%|#######6  | 23/30 [00:22<00:07,  1.01s/it]
Diffusion Sampling::  80%|########  | 24/30 [00:23<00:06,  1.01s/it]
Diffusion Sampling::  83%|########3 | 25/30 [00:24<00:05,  1.01s/it]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  87%|########6 | 26/30 [00:25<00:04,  1.01s/it]
Diffusion Sampling::  90%|######### | 27/30 [00:26<00:03,  1.01s/it]
Diffusion Sampling::  93%|#########3| 28/30 [00:27<00:02,  1.01s/it]
Diffusion Sampling::  97%|#########6| 29/30 [00:28<00:01,  1.01s/it]
Diffusion Sampling:: 100%|##########| 30/30 [00:29<00:00,  1.01it/s]

Volume Decoding:   0%|          | 0/1427 [00:00<?, ?it/s]
Volume Decoding:   0%|          | 5/1427 [00:00<00:28, 49.99it/s]
Volume Decoding:   3%|3         | 44/1427 [00:00<00:06, 217.27it/s]
Volume Decoding:   5%|4         | 65/1427 [00:01<00:41, 32.91it/s] 
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:   5%|5         | 77/1427 [00:02<00:53, 25.22it/s]
Volume Decoding:   6%|5         | 85/1427 [00:02<00:59, 22.42it/s]
Volume Decoding:   6%|6         | 91/1427 [00:03<01:05, 20.55it/s]
Volume Decoding:   7%|6         | 96/1427 [00:03<01:08, 19.32it/s]
Volume Decoding:   7%|7         | 100/1427 [00:03<01:11, 18.52it/s]
Volume Decoding:   7%|7         | 103/1427 [00:04<01:13, 18.06it/s]
Volume Decoding:   7%|7         | 106/1427 [00:04<01:15, 17.38it/s]
Volume Decoding:   8%|7         | 109/1427 [00:04<01:18, 16.74it/s]
Volume Decoding:   8%|7         | 111/1427 [00:04<01:19, 16.57it/s]
Volume Decoding:   8%|7         | 113/1427 [00:04<01:21, 16.07it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:   8%|8         | 115/1427 [00:04<01:21, 16.01it/s]
Volume Decoding:   8%|8         | 117/1427 [00:05<01:22, 15.93it/s]
Volume Decoding:   8%|8         | 119/1427 [00:05<01:24, 15.41it/s]
Volume Decoding:   8%|8         | 121/1427 [00:05<01:24, 15.51it/s]
Volume Decoding:   9%|8         | 123/1427 [00:05<01:26, 15.10it/s]
Volume Decoding:   9%|8         | 125/1427 [00:05<01:25, 15.24it/s]
Volume Decoding:   9%|8         | 127/1427 [00:05<01:24, 15.42it/s]
Volume Decoding:   9%|9         | 129/1427 [00:05<01:26, 14.95it/s]
Volume Decoding:   9%|9         | 131/1427 [00:06<01:25, 15.16it/s]
Volume Decoding:   9%|9         | 133/1427 [00:06<01:27, 14.83it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:17:52"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:   9%|9         | 135/1427 [00:06<01:25, 15.13it/s]
Volume Decoding:  10%|9         | 137/1427 [00:06<01:23, 15.40it/s]
Volume Decoding:  10%|9         | 139/1427 [00:06<01:26, 14.97it/s]
Volume Decoding:  10%|9         | 141/1427 [00:06<01:24, 15.24it/s]
Volume Decoding:  10%|#         | 143/1427 [00:06<01:26, 14.84it/s]
Volume Decoding:  10%|#         | 145/1427 [00:06<01:25, 15.05it/s]
Volume Decoding:  10%|#         | 147/1427 [00:07<01:26, 14.78it/s]
Volume Decoding:  10%|#         | 149/1427 [00:07<01:24, 15.05it/s]
Volume Decoding:  11%|#         | 151/1427 [00:07<01:24, 15.18it/s]
Volume Decoding:  11%|#         | 153/1427 [00:07<01:25, 14.83it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  11%|#         | 155/1427 [00:07<01:24, 15.10it/s]
Volume Decoding:  11%|#1        | 157/1427 [00:07<01:23, 15.27it/s]
Volume Decoding:  11%|#1        | 159/1427 [00:07<01:25, 14.91it/s]
Volume Decoding:  11%|#1        | 161/1427 [00:08<01:23, 15.15it/s]
Volume Decoding:  11%|#1        | 163/1427 [00:08<01:25, 14.79it/s]
Volume Decoding:  12%|#1        | 165/1427 [00:08<01:23, 15.06it/s]
Volume Decoding:  12%|#1        | 167/1427 [00:08<01:22, 15.24it/s]
Volume Decoding:  12%|#1        | 169/1427 [00:08<01:24, 14.81it/s]
Volume Decoding:  12%|#1        | 171/1427 [00:08<01:23, 15.08it/s]
Volume Decoding:  12%|#2        | 173/1427 [00:08<01:24, 14.89it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  12%|#2        | 175/1427 [00:08<01:22, 15.19it/s]
Volume Decoding:  12%|#2        | 177/1427 [00:09<01:21, 15.31it/s]
Volume Decoding:  13%|#2        | 179/1427 [00:09<01:23, 14.92it/s]
Volume Decoding:  13%|#2        | 181/1427 [00:09<01:22, 15.16it/s]
Volume Decoding:  13%|#2        | 183/1427 [00:09<01:23, 14.84it/s]
Volume Decoding:  13%|#2        | 185/1427 [00:09<01:22, 15.07it/s]
Volume Decoding:  13%|#3        | 187/1427 [00:09<01:21, 15.23it/s]
Volume Decoding:  13%|#3        | 189/1427 [00:09<01:23, 14.89it/s]
Volume Decoding:  13%|#3        | 191/1427 [00:10<01:21, 15.11it/s]
Volume Decoding:  14%|#3        | 193/1427 [00:10<01:23, 14.85it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  14%|#3        | 195/1427 [00:10<01:21, 15.11it/s]
Volume Decoding:  14%|#3        | 197/1427 [00:10<01:20, 15.26it/s]
Volume Decoding:  14%|#3        | 199/1427 [00:10<01:22, 14.85it/s]
Volume Decoding:  14%|#4        | 201/1427 [00:10<01:20, 15.17it/s]
Volume Decoding:  14%|#4        | 203/1427 [00:10<01:22, 14.76it/s]
Volume Decoding:  14%|#4        | 205/1427 [00:10<01:20, 15.15it/s]
Volume Decoding:  15%|#4        | 207/1427 [00:11<01:19, 15.29it/s]
Volume Decoding:  15%|#4        | 209/1427 [00:11<01:21, 15.02it/s]
Volume Decoding:  15%|#4        | 211/1427 [00:11<01:22, 14.80it/s]
Volume Decoding:  15%|#4        | 213/1427 [00:11<01:20, 15.11it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  15%|#5        | 215/1427 [00:11<01:21, 14.84it/s]
Volume Decoding:  15%|#5        | 217/1427 [00:11<01:19, 15.15it/s]
Volume Decoding:  15%|#5        | 219/1427 [00:11<01:21, 14.88it/s]
Volume Decoding:  15%|#5        | 221/1427 [00:12<01:19, 15.08it/s]
Volume Decoding:  16%|#5        | 223/1427 [00:12<01:18, 15.30it/s]
Volume Decoding:  16%|#5        | 225/1427 [00:12<01:20, 14.91it/s]
Volume Decoding:  16%|#5        | 227/1427 [00:12<01:19, 15.19it/s]
Volume Decoding:  16%|#6        | 229/1427 [00:12<01:20, 14.87it/s]
Volume Decoding:  16%|#6        | 231/1427 [00:12<01:18, 15.18it/s]
Volume Decoding:  16%|#6        | 233/1427 [00:12<01:20, 14.80it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:17:59"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  16%|#6        | 235/1427 [00:12<01:18, 15.17it/s]
Volume Decoding:  17%|#6        | 237/1427 [00:13<01:17, 15.32it/s]
Volume Decoding:  17%|#6        | 239/1427 [00:13<01:19, 14.95it/s]
Volume Decoding:  17%|#6        | 241/1427 [00:13<01:17, 15.22it/s]
Volume Decoding:  17%|#7        | 243/1427 [00:13<01:19, 14.82it/s]
Volume Decoding:  17%|#7        | 245/1427 [00:13<01:18, 15.12it/s]
Volume Decoding:  17%|#7        | 247/1427 [00:13<01:19, 14.93it/s]
Volume Decoding:  17%|#7        | 249/1427 [00:13<01:17, 15.19it/s]
Volume Decoding:  18%|#7        | 251/1427 [00:14<01:19, 14.83it/s]
Volume Decoding:  18%|#7        | 253/1427 [00:14<01:17, 15.14it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  18%|#7        | 255/1427 [00:14<01:19, 14.77it/s]
Volume Decoding:  18%|#8        | 257/1427 [00:14<01:17, 15.03it/s]
Volume Decoding:  18%|#8        | 259/1427 [00:14<01:16, 15.27it/s]
Volume Decoding:  18%|#8        | 261/1427 [00:14<01:18, 14.88it/s]
Volume Decoding:  18%|#8        | 263/1427 [00:14<01:16, 15.16it/s]
Volume Decoding:  19%|#8        | 265/1427 [00:14<01:18, 14.81it/s]
Volume Decoding:  19%|#8        | 267/1427 [00:15<01:17, 15.05it/s]
Volume Decoding:  19%|#8        | 269/1427 [00:15<01:18, 14.83it/s]
Volume Decoding:  19%|#8        | 271/1427 [00:15<01:16, 15.16it/s]
Volume Decoding:  19%|#9        | 273/1427 [00:15<01:15, 15.30it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  19%|#9        | 275/1427 [00:15<01:17, 14.91it/s]
Volume Decoding:  19%|#9        | 277/1427 [00:15<01:17, 14.75it/s]
Volume Decoding:  20%|#9        | 279/1427 [00:15<01:16, 14.97it/s]
Volume Decoding:  20%|#9        | 281/1427 [00:16<01:17, 14.74it/s]
Volume Decoding:  20%|#9        | 283/1427 [00:16<01:15, 15.10it/s]
Volume Decoding:  20%|#9        | 285/1427 [00:16<01:17, 14.79it/s]
Volume Decoding:  20%|##        | 287/1427 [00:16<01:15, 15.06it/s]
Volume Decoding:  20%|##        | 289/1427 [00:16<01:17, 14.72it/s]
Volume Decoding:  20%|##        | 291/1427 [00:16<01:18, 14.49it/s]
Volume Decoding:  21%|##        | 293/1427 [00:16<01:16, 14.81it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  21%|##        | 295/1427 [00:16<01:17, 14.52it/s]
Volume Decoding:  21%|##        | 297/1427 [00:17<01:16, 14.85it/s]
Volume Decoding:  21%|##        | 299/1427 [00:17<01:17, 14.60it/s]
Volume Decoding:  21%|##1       | 301/1427 [00:17<01:15, 14.92it/s]
Volume Decoding:  21%|##1       | 303/1427 [00:17<01:14, 15.12it/s]
Volume Decoding:  21%|##1       | 305/1427 [00:17<01:15, 14.79it/s]
Volume Decoding:  22%|##1       | 307/1427 [00:17<01:14, 15.10it/s]
Volume Decoding:  22%|##1       | 309/1427 [00:17<01:15, 14.72it/s]
Volume Decoding:  22%|##1       | 311/1427 [00:18<01:14, 15.01it/s]
Volume Decoding:  22%|##1       | 313/1427 [00:18<01:15, 14.70it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  22%|##2       | 315/1427 [00:18<01:14, 14.93it/s]
Volume Decoding:  22%|##2       | 317/1427 [00:18<01:15, 14.66it/s]
Volume Decoding:  22%|##2       | 319/1427 [00:18<01:16, 14.54it/s]
Volume Decoding:  22%|##2       | 321/1427 [00:18<01:13, 14.99it/s]
Volume Decoding:  23%|##2       | 323/1427 [00:18<01:15, 14.69it/s]
Volume Decoding:  23%|##2       | 325/1427 [00:18<01:13, 14.99it/s]
Volume Decoding:  23%|##2       | 327/1427 [00:19<01:15, 14.62it/s]
Volume Decoding:  23%|##3       | 329/1427 [00:19<01:13, 14.93it/s]
Volume Decoding:  23%|##3       | 331/1427 [00:19<01:14, 14.69it/s]
Volume Decoding:  23%|##3       | 333/1427 [00:19<01:12, 14.99it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:05"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  23%|##3       | 335/1427 [00:19<01:14, 14.67it/s]
Volume Decoding:  24%|##3       | 337/1427 [00:19<01:14, 14.55it/s]
Volume Decoding:  24%|##3       | 339/1427 [00:19<01:13, 14.89it/s]
Volume Decoding:  24%|##3       | 341/1427 [00:20<01:14, 14.67it/s]
Volume Decoding:  24%|##4       | 343/1427 [00:20<01:12, 14.97it/s]
Volume Decoding:  24%|##4       | 345/1427 [00:20<01:13, 14.70it/s]
Volume Decoding:  24%|##4       | 347/1427 [00:20<01:14, 14.50it/s]
Volume Decoding:  24%|##4       | 349/1427 [00:20<01:12, 14.88it/s]
Volume Decoding:  25%|##4       | 351/1427 [00:20<01:13, 14.63it/s]
Volume Decoding:  25%|##4       | 353/1427 [00:20<01:14, 14.40it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  25%|##4       | 355/1427 [00:21<01:14, 14.37it/s]
Volume Decoding:  25%|##5       | 357/1427 [00:21<01:14, 14.39it/s]
Volume Decoding:  25%|##5       | 359/1427 [00:21<01:14, 14.28it/s]
Volume Decoding:  25%|##5       | 361/1427 [00:21<01:12, 14.79it/s]
Volume Decoding:  25%|##5       | 363/1427 [00:21<01:12, 14.65it/s]
Volume Decoding:  26%|##5       | 365/1427 [00:21<01:11, 14.94it/s]
Volume Decoding:  26%|##5       | 367/1427 [00:21<01:12, 14.64it/s]
Volume Decoding:  26%|##5       | 369/1427 [00:21<01:11, 14.89it/s]
Volume Decoding:  26%|##5       | 371/1427 [00:22<01:09, 15.18it/s]
Volume Decoding:  26%|##6       | 373/1427 [00:22<01:10, 14.88it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  26%|##6       | 375/1427 [00:22<01:09, 15.18it/s]
Volume Decoding:  26%|##6       | 377/1427 [00:22<01:10, 14.83it/s]
Volume Decoding:  27%|##6       | 379/1427 [00:22<01:09, 15.13it/s]
Volume Decoding:  27%|##6       | 381/1427 [00:22<01:10, 14.84it/s]
Volume Decoding:  27%|##6       | 383/1427 [00:22<01:09, 15.06it/s]
Volume Decoding:  27%|##6       | 385/1427 [00:23<01:08, 15.28it/s]
Volume Decoding:  27%|##7       | 387/1427 [00:23<01:10, 14.84it/s]
Volume Decoding:  27%|##7       | 389/1427 [00:23<01:08, 15.09it/s]
Volume Decoding:  27%|##7       | 391/1427 [00:23<01:10, 14.76it/s]
Volume Decoding:  28%|##7       | 393/1427 [00:23<01:10, 14.65it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  28%|##7       | 395/1427 [00:23<01:08, 15.06it/s]
Volume Decoding:  28%|##7       | 397/1427 [00:23<01:09, 14.76it/s]
Volume Decoding:  28%|##7       | 399/1427 [00:23<01:08, 15.04it/s]
Volume Decoding:  28%|##8       | 401/1427 [00:24<01:07, 15.28it/s]
Volume Decoding:  28%|##8       | 403/1427 [00:24<01:08, 14.93it/s]
Volume Decoding:  28%|##8       | 405/1427 [00:24<01:07, 15.14it/s]
Volume Decoding:  29%|##8       | 407/1427 [00:24<01:09, 14.77it/s]
Volume Decoding:  29%|##8       | 409/1427 [00:24<01:07, 15.06it/s]
Volume Decoding:  29%|##8       | 411/1427 [00:24<01:08, 14.76it/s]
Volume Decoding:  29%|##8       | 413/1427 [00:24<01:07, 15.01it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  29%|##9       | 415/1427 [00:25<01:06, 15.28it/s]
Volume Decoding:  29%|##9       | 417/1427 [00:25<01:08, 14.84it/s]
Volume Decoding:  29%|##9       | 419/1427 [00:25<01:06, 15.16it/s]
Volume Decoding:  30%|##9       | 421/1427 [00:25<01:07, 14.82it/s]
Volume Decoding:  30%|##9       | 423/1427 [00:25<01:06, 15.05it/s]
Volume Decoding:  30%|##9       | 425/1427 [00:25<01:05, 15.29it/s]
Volume Decoding:  30%|##9       | 427/1427 [00:25<01:07, 14.87it/s]
Volume Decoding:  30%|###       | 429/1427 [00:25<01:05, 15.19it/s]
Volume Decoding:  30%|###       | 431/1427 [00:26<01:06, 14.92it/s]
Volume Decoding:  30%|###       | 433/1427 [00:26<01:05, 15.15it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:12"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  30%|###       | 435/1427 [00:26<01:04, 15.32it/s]
Volume Decoding:  31%|###       | 437/1427 [00:26<01:06, 14.89it/s]
Volume Decoding:  31%|###       | 439/1427 [00:26<01:04, 15.24it/s]
Volume Decoding:  31%|###       | 441/1427 [00:26<01:06, 14.88it/s]
Volume Decoding:  31%|###1      | 443/1427 [00:26<01:04, 15.14it/s]
Volume Decoding:  31%|###1      | 445/1427 [00:27<01:06, 14.71it/s]
Volume Decoding:  31%|###1      | 447/1427 [00:27<01:05, 14.97it/s]
Volume Decoding:  31%|###1      | 449/1427 [00:27<01:04, 15.18it/s]
Volume Decoding:  32%|###1      | 451/1427 [00:27<01:05, 14.83it/s]
Volume Decoding:  32%|###1      | 453/1427 [00:27<01:04, 15.06it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  32%|###1      | 455/1427 [00:27<01:05, 14.74it/s]
Volume Decoding:  32%|###2      | 457/1427 [00:27<01:04, 15.13it/s]
Volume Decoding:  32%|###2      | 459/1427 [00:27<01:05, 14.80it/s]
Volume Decoding:  32%|###2      | 461/1427 [00:28<01:06, 14.60it/s]
Volume Decoding:  32%|###2      | 463/1427 [00:28<01:04, 14.94it/s]
Volume Decoding:  33%|###2      | 465/1427 [00:28<01:03, 15.14it/s]
Volume Decoding:  33%|###2      | 467/1427 [00:28<01:04, 14.92it/s]
Volume Decoding:  33%|###2      | 469/1427 [00:28<01:03, 15.16it/s]
Volume Decoding:  33%|###3      | 471/1427 [00:28<01:03, 14.94it/s]
Volume Decoding:  33%|###3      | 473/1427 [00:28<01:03, 15.13it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  33%|###3      | 475/1427 [00:29<01:02, 15.27it/s]
Volume Decoding:  33%|###3      | 477/1427 [00:29<01:03, 14.91it/s]
Volume Decoding:  34%|###3      | 479/1427 [00:29<01:02, 15.15it/s]
Volume Decoding:  34%|###3      | 481/1427 [00:29<01:03, 14.82it/s]
Volume Decoding:  34%|###3      | 483/1427 [00:29<01:02, 15.08it/s]
Volume Decoding:  34%|###3      | 485/1427 [00:29<01:03, 14.79it/s]
Volume Decoding:  34%|###4      | 487/1427 [00:29<01:02, 15.03it/s]
Volume Decoding:  34%|###4      | 489/1427 [00:29<01:03, 14.70it/s]
Volume Decoding:  34%|###4      | 491/1427 [00:30<01:02, 14.99it/s]
Volume Decoding:  35%|###4      | 493/1427 [00:30<01:03, 14.65it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  35%|###4      | 495/1427 [00:30<01:02, 14.99it/s]
Volume Decoding:  35%|###4      | 497/1427 [00:30<01:03, 14.69it/s]
Volume Decoding:  35%|###4      | 499/1427 [00:30<01:01, 14.99it/s]
Volume Decoding:  35%|###5      | 501/1427 [00:30<01:00, 15.21it/s]
Volume Decoding:  35%|###5      | 503/1427 [00:30<01:01, 14.92it/s]
Volume Decoding:  35%|###5      | 505/1427 [00:31<01:00, 15.21it/s]
Volume Decoding:  36%|###5      | 507/1427 [00:31<01:01, 14.84it/s]
Volume Decoding:  36%|###5      | 509/1427 [00:31<01:00, 15.18it/s]
Volume Decoding:  36%|###5      | 511/1427 [00:31<00:59, 15.35it/s]
Volume Decoding:  36%|###5      | 513/1427 [00:31<01:01, 14.85it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  36%|###6      | 515/1427 [00:31<01:00, 15.09it/s]
Volume Decoding:  36%|###6      | 517/1427 [00:31<01:01, 14.87it/s]
Volume Decoding:  36%|###6      | 519/1427 [00:31<00:59, 15.16it/s]
Volume Decoding:  37%|###6      | 521/1427 [00:32<01:01, 14.81it/s]
Volume Decoding:  37%|###6      | 523/1427 [00:32<00:59, 15.07it/s]
Volume Decoding:  37%|###6      | 525/1427 [00:32<00:59, 15.27it/s]
Volume Decoding:  37%|###6      | 527/1427 [00:32<01:00, 14.89it/s]
Volume Decoding:  37%|###7      | 529/1427 [00:32<00:59, 15.14it/s]
Volume Decoding:  37%|###7      | 531/1427 [00:32<01:00, 14.84it/s]
Volume Decoding:  37%|###7      | 533/1427 [00:32<01:00, 14.66it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:19"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  37%|###7      | 535/1427 [00:33<00:59, 14.97it/s]
Volume Decoding:  38%|###7      | 537/1427 [00:33<00:58, 15.24it/s]
Volume Decoding:  38%|###7      | 539/1427 [00:33<00:59, 14.93it/s]
Volume Decoding:  38%|###7      | 541/1427 [00:33<00:59, 14.85it/s]
Volume Decoding:  38%|###8      | 543/1427 [00:33<00:58, 15.06it/s]
Volume Decoding:  38%|###8      | 545/1427 [00:33<00:59, 14.76it/s]
Volume Decoding:  38%|###8      | 547/1427 [00:33<00:58, 15.13it/s]
Volume Decoding:  38%|###8      | 549/1427 [00:33<00:59, 14.83it/s]
Volume Decoding:  39%|###8      | 551/1427 [00:34<00:58, 15.09it/s]
Volume Decoding:  39%|###8      | 553/1427 [00:34<00:57, 15.22it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  39%|###8      | 555/1427 [00:34<00:58, 14.88it/s]
Volume Decoding:  39%|###9      | 557/1427 [00:34<00:57, 15.11it/s]
Volume Decoding:  39%|###9      | 559/1427 [00:34<00:56, 15.30it/s]
Volume Decoding:  39%|###9      | 561/1427 [00:34<00:58, 14.91it/s]
Volume Decoding:  39%|###9      | 563/1427 [00:34<00:57, 15.15it/s]
Volume Decoding:  40%|###9      | 565/1427 [00:35<00:58, 14.75it/s]
Volume Decoding:  40%|###9      | 567/1427 [00:35<00:56, 15.12it/s]
Volume Decoding:  40%|###9      | 569/1427 [00:35<00:58, 14.74it/s]
Volume Decoding:  40%|####      | 571/1427 [00:35<00:56, 15.10it/s]
Volume Decoding:  40%|####      | 573/1427 [00:35<00:56, 15.23it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  40%|####      | 575/1427 [00:35<00:57, 14.88it/s]
Volume Decoding:  40%|####      | 577/1427 [00:35<00:55, 15.24it/s]
Volume Decoding:  41%|####      | 579/1427 [00:35<00:57, 14.85it/s]
Volume Decoding:  41%|####      | 581/1427 [00:36<00:56, 15.04it/s]
Volume Decoding:  41%|####      | 583/1427 [00:36<00:56, 14.82it/s]
Volume Decoding:  41%|####      | 585/1427 [00:36<00:55, 15.10it/s]
Volume Decoding:  41%|####1     | 587/1427 [00:36<00:56, 14.76it/s]
Volume Decoding:  41%|####1     | 589/1427 [00:36<00:55, 15.04it/s]
Volume Decoding:  41%|####1     | 591/1427 [00:36<00:54, 15.25it/s]
Volume Decoding:  42%|####1     | 593/1427 [00:36<00:56, 14.81it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  42%|####1     | 595/1427 [00:37<00:55, 15.05it/s]
Volume Decoding:  42%|####1     | 597/1427 [00:37<00:56, 14.71it/s]
Volume Decoding:  42%|####1     | 599/1427 [00:37<00:55, 14.94it/s]
Volume Decoding:  42%|####2     | 601/1427 [00:37<00:54, 15.23it/s]
Volume Decoding:  42%|####2     | 603/1427 [00:37<00:55, 14.80it/s]
Volume Decoding:  42%|####2     | 605/1427 [00:37<00:54, 15.07it/s]
Volume Decoding:  43%|####2     | 607/1427 [00:37<00:55, 14.70it/s]
Volume Decoding:  43%|####2     | 609/1427 [00:37<00:54, 15.02it/s]
Volume Decoding:  43%|####2     | 611/1427 [00:38<00:55, 14.76it/s]
Volume Decoding:  43%|####2     | 613/1427 [00:38<00:53, 15.13it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  43%|####3     | 615/1427 [00:38<00:54, 14.88it/s]
Volume Decoding:  43%|####3     | 617/1427 [00:38<00:53, 15.14it/s]
Volume Decoding:  43%|####3     | 619/1427 [00:38<00:54, 14.81it/s]
Volume Decoding:  44%|####3     | 621/1427 [00:38<00:53, 15.11it/s]
Volume Decoding:  44%|####3     | 623/1427 [00:38<00:54, 14.85it/s]
Volume Decoding:  44%|####3     | 625/1427 [00:39<00:52, 15.14it/s]
Volume Decoding:  44%|####3     | 627/1427 [00:39<00:52, 15.31it/s]
Volume Decoding:  44%|####4     | 629/1427 [00:39<00:53, 14.88it/s]
Volume Decoding:  44%|####4     | 631/1427 [00:39<00:52, 15.19it/s]
Volume Decoding:  44%|####4     | 633/1427 [00:39<00:53, 14.81it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:25"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  44%|####4     | 635/1427 [00:39<00:52, 15.04it/s]
Volume Decoding:  45%|####4     | 637/1427 [00:39<00:53, 14.74it/s]
Volume Decoding:  45%|####4     | 639/1427 [00:39<00:52, 15.03it/s]
Volume Decoding:  45%|####4     | 641/1427 [00:40<00:51, 15.31it/s]
Volume Decoding:  45%|####5     | 643/1427 [00:40<00:52, 14.90it/s]
Volume Decoding:  45%|####5     | 645/1427 [00:40<00:51, 15.13it/s]
Volume Decoding:  45%|####5     | 647/1427 [00:40<00:52, 14.85it/s]
Volume Decoding:  45%|####5     | 649/1427 [00:40<00:51, 15.10it/s]
Volume Decoding:  46%|####5     | 651/1427 [00:40<00:52, 14.85it/s]
Volume Decoding:  46%|####5     | 653/1427 [00:40<00:51, 15.12it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  46%|####5     | 655/1427 [00:41<00:52, 14.76it/s]
Volume Decoding:  46%|####6     | 657/1427 [00:41<00:51, 15.01it/s]
Volume Decoding:  46%|####6     | 659/1427 [00:41<00:50, 15.25it/s]
Volume Decoding:  46%|####6     | 661/1427 [00:41<00:51, 14.89it/s]
Volume Decoding:  46%|####6     | 663/1427 [00:41<00:50, 15.14it/s]
Volume Decoding:  47%|####6     | 665/1427 [00:41<00:51, 14.78it/s]
Volume Decoding:  47%|####6     | 667/1427 [00:41<00:52, 14.59it/s]
Volume Decoding:  47%|####6     | 669/1427 [00:41<00:50, 14.95it/s]
Volume Decoding:  47%|####7     | 671/1427 [00:42<00:51, 14.58it/s]
Volume Decoding:  47%|####7     | 673/1427 [00:42<00:50, 14.97it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  47%|####7     | 675/1427 [00:42<00:51, 14.69it/s]
Volume Decoding:  47%|####7     | 677/1427 [00:42<00:50, 14.99it/s]
Volume Decoding:  48%|####7     | 679/1427 [00:42<00:50, 14.72it/s]
Volume Decoding:  48%|####7     | 681/1427 [00:42<00:49, 15.02it/s]
Volume Decoding:  48%|####7     | 683/1427 [00:42<00:50, 14.69it/s]
Volume Decoding:  48%|####8     | 685/1427 [00:43<00:49, 14.99it/s]
Volume Decoding:  48%|####8     | 687/1427 [00:43<00:50, 14.77it/s]
Volume Decoding:  48%|####8     | 689/1427 [00:43<00:48, 15.14it/s]
Volume Decoding:  48%|####8     | 691/1427 [00:43<00:49, 14.73it/s]
Volume Decoding:  49%|####8     | 693/1427 [00:43<00:48, 14.98it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  49%|####8     | 695/1427 [00:43<00:49, 14.67it/s]
Volume Decoding:  49%|####8     | 697/1427 [00:43<00:48, 14.98it/s]
Volume Decoding:  49%|####8     | 699/1427 [00:44<00:49, 14.73it/s]
Volume Decoding:  49%|####9     | 701/1427 [00:44<00:48, 15.09it/s]
Volume Decoding:  49%|####9     | 703/1427 [00:44<00:49, 14.77it/s]
Volume Decoding:  49%|####9     | 705/1427 [00:44<00:47, 15.06it/s]
Volume Decoding:  50%|####9     | 707/1427 [00:44<00:47, 15.31it/s]
Volume Decoding:  50%|####9     | 709/1427 [00:44<00:48, 14.83it/s]
Volume Decoding:  50%|####9     | 711/1427 [00:44<00:47, 15.15it/s]
Volume Decoding:  50%|####9     | 713/1427 [00:44<00:47, 14.90it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  50%|#####     | 715/1427 [00:45<00:46, 15.17it/s]
Volume Decoding:  50%|#####     | 717/1427 [00:45<00:47, 14.93it/s]
Volume Decoding:  50%|#####     | 719/1427 [00:45<00:46, 15.20it/s]
Volume Decoding:  51%|#####     | 721/1427 [00:45<00:47, 14.84it/s]
Volume Decoding:  51%|#####     | 723/1427 [00:45<00:46, 15.16it/s]
Volume Decoding:  51%|#####     | 725/1427 [00:45<00:47, 14.88it/s]
Volume Decoding:  51%|#####     | 727/1427 [00:45<00:46, 15.11it/s]
Volume Decoding:  51%|#####1    | 729/1427 [00:46<00:47, 14.85it/s]
Volume Decoding:  51%|#####1    | 731/1427 [00:46<00:47, 14.65it/s]
Volume Decoding:  51%|#####1    | 733/1427 [00:46<00:46, 15.07it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:32"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  52%|#####1    | 735/1427 [00:46<00:47, 14.70it/s]
Volume Decoding:  52%|#####1    | 737/1427 [00:46<00:46, 14.97it/s]
Volume Decoding:  52%|#####1    | 739/1427 [00:46<00:46, 14.68it/s]
Volume Decoding:  52%|#####1    | 741/1427 [00:46<00:45, 14.95it/s]
Volume Decoding:  52%|#####2    | 743/1427 [00:46<00:46, 14.75it/s]
Volume Decoding:  52%|#####2    | 745/1427 [00:47<00:46, 14.55it/s]
Volume Decoding:  52%|#####2    | 747/1427 [00:47<00:45, 14.99it/s]
Volume Decoding:  52%|#####2    | 749/1427 [00:47<00:46, 14.70it/s]
Volume Decoding:  53%|#####2    | 751/1427 [00:47<00:44, 15.04it/s]
Volume Decoding:  53%|#####2    | 753/1427 [00:47<00:45, 14.72it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  53%|#####2    | 755/1427 [00:47<00:46, 14.56it/s]
Volume Decoding:  53%|#####3    | 757/1427 [00:47<00:45, 14.88it/s]
Volume Decoding:  53%|#####3    | 759/1427 [00:48<00:45, 14.65it/s]
Volume Decoding:  53%|#####3    | 761/1427 [00:48<00:44, 15.11it/s]
Volume Decoding:  53%|#####3    | 763/1427 [00:48<00:44, 14.81it/s]
Volume Decoding:  54%|#####3    | 765/1427 [00:48<00:43, 15.12it/s]
Volume Decoding:  54%|#####3    | 767/1427 [00:48<00:44, 14.73it/s]
Volume Decoding:  54%|#####3    | 769/1427 [00:48<00:43, 15.05it/s]
Volume Decoding:  54%|#####4    | 771/1427 [00:48<00:44, 14.78it/s]
Volume Decoding:  54%|#####4    | 773/1427 [00:48<00:45, 14.53it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  54%|#####4    | 775/1427 [00:49<00:43, 14.99it/s]
Volume Decoding:  54%|#####4    | 777/1427 [00:49<00:44, 14.73it/s]
Volume Decoding:  55%|#####4    | 779/1427 [00:49<00:43, 15.01it/s]
Volume Decoding:  55%|#####4    | 781/1427 [00:49<00:44, 14.68it/s]
Volume Decoding:  55%|#####4    | 783/1427 [00:49<00:42, 15.03it/s]
Volume Decoding:  55%|#####5    | 785/1427 [00:49<00:42, 15.28it/s]
Volume Decoding:  55%|#####5    | 787/1427 [00:49<00:43, 14.86it/s]
Volume Decoding:  55%|#####5    | 789/1427 [00:50<00:41, 15.22it/s]
Volume Decoding:  55%|#####5    | 791/1427 [00:50<00:42, 14.88it/s]
Volume Decoding:  56%|#####5    | 793/1427 [00:50<00:41, 15.21it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  56%|#####5    | 795/1427 [00:50<00:42, 14.84it/s]
Volume Decoding:  56%|#####5    | 797/1427 [00:50<00:42, 14.74it/s]
Volume Decoding:  56%|#####5    | 799/1427 [00:50<00:41, 15.09it/s]
Volume Decoding:  56%|#####6    | 801/1427 [00:50<00:42, 14.77it/s]
Volume Decoding:  56%|#####6    | 803/1427 [00:50<00:41, 15.19it/s]
Volume Decoding:  56%|#####6    | 805/1427 [00:51<00:41, 14.87it/s]
Volume Decoding:  57%|#####6    | 807/1427 [00:51<00:40, 15.15it/s]
Volume Decoding:  57%|#####6    | 809/1427 [00:51<00:41, 14.90it/s]
Volume Decoding:  57%|#####6    | 811/1427 [00:51<00:40, 15.16it/s]
Volume Decoding:  57%|#####6    | 813/1427 [00:51<00:41, 14.84it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  57%|#####7    | 815/1427 [00:51<00:40, 15.03it/s]
Volume Decoding:  57%|#####7    | 817/1427 [00:51<00:39, 15.28it/s]
Volume Decoding:  57%|#####7    | 819/1427 [00:52<00:40, 14.90it/s]
Volume Decoding:  58%|#####7    | 821/1427 [00:52<00:39, 15.22it/s]
Volume Decoding:  58%|#####7    | 823/1427 [00:52<00:40, 14.91it/s]
Volume Decoding:  58%|#####7    | 825/1427 [00:52<00:39, 15.12it/s]
Volume Decoding:  58%|#####7    | 827/1427 [00:52<00:40, 14.83it/s]
Volume Decoding:  58%|#####8    | 829/1427 [00:52<00:39, 15.10it/s]
Volume Decoding:  58%|#####8    | 831/1427 [00:52<00:40, 14.78it/s]
Volume Decoding:  58%|#####8    | 833/1427 [00:52<00:39, 15.15it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:39"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  59%|#####8    | 835/1427 [00:53<00:39, 14.90it/s]
Volume Decoding:  59%|#####8    | 837/1427 [00:53<00:38, 15.20it/s]
Volume Decoding:  59%|#####8    | 839/1427 [00:53<00:39, 14.87it/s]
Volume Decoding:  59%|#####8    | 841/1427 [00:53<00:38, 15.13it/s]
Volume Decoding:  59%|#####9    | 843/1427 [00:53<00:39, 14.84it/s]
Volume Decoding:  59%|#####9    | 845/1427 [00:53<00:38, 15.03it/s]
Volume Decoding:  59%|#####9    | 847/1427 [00:53<00:38, 15.24it/s]
Volume Decoding:  59%|#####9    | 849/1427 [00:54<00:38, 14.93it/s]
Volume Decoding:  60%|#####9    | 851/1427 [00:54<00:37, 15.16it/s]
Volume Decoding:  60%|#####9    | 853/1427 [00:54<00:38, 14.87it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  60%|#####9    | 855/1427 [00:54<00:37, 15.18it/s]
Volume Decoding:  60%|######    | 857/1427 [00:54<00:38, 14.80it/s]
Volume Decoding:  60%|######    | 859/1427 [00:54<00:37, 15.08it/s]
Volume Decoding:  60%|######    | 861/1427 [00:54<00:38, 14.76it/s]
Volume Decoding:  60%|######    | 863/1427 [00:54<00:37, 15.07it/s]
Volume Decoding:  61%|######    | 865/1427 [00:55<00:36, 15.31it/s]
Volume Decoding:  61%|######    | 867/1427 [00:55<00:37, 14.99it/s]
Volume Decoding:  61%|######    | 869/1427 [00:55<00:36, 15.26it/s]
Volume Decoding:  61%|######1   | 871/1427 [00:55<00:37, 14.97it/s]
Volume Decoding:  61%|######1   | 873/1427 [00:55<00:37, 14.68it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  61%|######1   | 875/1427 [00:55<00:36, 15.06it/s]
Volume Decoding:  61%|######1   | 877/1427 [00:55<00:37, 14.78it/s]
Volume Decoding:  62%|######1   | 879/1427 [00:56<00:36, 15.10it/s]
Volume Decoding:  62%|######1   | 881/1427 [00:56<00:35, 15.28it/s]
Volume Decoding:  62%|######1   | 883/1427 [00:56<00:36, 14.91it/s]
Volume Decoding:  62%|######2   | 885/1427 [00:56<00:35, 15.19it/s]
Volume Decoding:  62%|######2   | 887/1427 [00:56<00:36, 14.84it/s]
Volume Decoding:  62%|######2   | 889/1427 [00:56<00:35, 15.15it/s]
Volume Decoding:  62%|######2   | 891/1427 [00:56<00:36, 14.85it/s]
Volume Decoding:  63%|######2   | 893/1427 [00:56<00:35, 15.19it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  63%|######2   | 895/1427 [00:57<00:35, 14.86it/s]
Volume Decoding:  63%|######2   | 897/1427 [00:57<00:35, 14.73it/s]
Volume Decoding:  63%|######2   | 899/1427 [00:57<00:34, 15.09it/s]
Volume Decoding:  63%|######3   | 901/1427 [00:57<00:35, 14.90it/s]
Volume Decoding:  63%|######3   | 903/1427 [00:57<00:34, 15.11it/s]
Volume Decoding:  63%|######3   | 905/1427 [00:57<00:35, 14.79it/s]
Volume Decoding:  64%|######3   | 907/1427 [00:57<00:34, 15.11it/s]
Volume Decoding:  64%|######3   | 909/1427 [00:58<00:34, 14.96it/s]
Volume Decoding:  64%|######3   | 911/1427 [00:58<00:33, 15.25it/s]
Volume Decoding:  64%|######3   | 913/1427 [00:58<00:34, 14.91it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  64%|######4   | 915/1427 [00:58<00:33, 15.21it/s]
Volume Decoding:  64%|######4   | 917/1427 [00:58<00:34, 14.95it/s]
Volume Decoding:  64%|######4   | 919/1427 [00:58<00:33, 15.17it/s]
Volume Decoding:  65%|######4   | 921/1427 [00:58<00:34, 14.83it/s]
Volume Decoding:  65%|######4   | 923/1427 [00:58<00:33, 15.09it/s]
Volume Decoding:  65%|######4   | 925/1427 [00:59<00:34, 14.76it/s]
Volume Decoding:  65%|######4   | 927/1427 [00:59<00:33, 15.12it/s]
Volume Decoding:  65%|######5   | 929/1427 [00:59<00:33, 14.86it/s]
Volume Decoding:  65%|######5   | 931/1427 [00:59<00:32, 15.12it/s]
Volume Decoding:  65%|######5   | 933/1427 [00:59<00:33, 14.72it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:45"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  66%|######5   | 935/1427 [00:59<00:32, 15.10it/s]
Volume Decoding:  66%|######5   | 937/1427 [00:59<00:33, 14.78it/s]
Volume Decoding:  66%|######5   | 939/1427 [01:00<00:32, 15.09it/s]
Volume Decoding:  66%|######5   | 941/1427 [01:00<00:31, 15.25it/s]
Volume Decoding:  66%|######6   | 943/1427 [01:00<00:32, 14.88it/s]
Volume Decoding:  66%|######6   | 945/1427 [01:00<00:31, 15.23it/s]
Volume Decoding:  66%|######6   | 947/1427 [01:00<00:32, 14.89it/s]
Volume Decoding:  67%|######6   | 949/1427 [01:00<00:31, 15.16it/s]
Volume Decoding:  67%|######6   | 951/1427 [01:00<00:32, 14.82it/s]
Volume Decoding:  67%|######6   | 953/1427 [01:00<00:31, 15.16it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  67%|######6   | 955/1427 [01:01<00:31, 14.79it/s]
Volume Decoding:  67%|######7   | 957/1427 [01:01<00:31, 15.13it/s]
Volume Decoding:  67%|######7   | 959/1427 [01:01<00:31, 14.83it/s]
Volume Decoding:  67%|######7   | 961/1427 [01:01<00:30, 15.06it/s]
Volume Decoding:  67%|######7   | 963/1427 [01:01<00:31, 14.79it/s]
Volume Decoding:  68%|######7   | 965/1427 [01:01<00:30, 15.01it/s]
Volume Decoding:  68%|######7   | 967/1427 [01:01<00:30, 15.28it/s]
Volume Decoding:  68%|######7   | 969/1427 [01:02<00:30, 14.87it/s]
Volume Decoding:  68%|######8   | 971/1427 [01:02<00:30, 15.15it/s]
Volume Decoding:  68%|######8   | 973/1427 [01:02<00:30, 14.78it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  68%|######8   | 975/1427 [01:02<00:29, 15.10it/s]
Volume Decoding:  68%|######8   | 977/1427 [01:02<00:29, 15.27it/s]
Volume Decoding:  69%|######8   | 979/1427 [01:02<00:30, 14.87it/s]
Volume Decoding:  69%|######8   | 981/1427 [01:02<00:29, 15.21it/s]
Volume Decoding:  69%|######8   | 983/1427 [01:02<00:29, 14.91it/s]
Volume Decoding:  69%|######9   | 985/1427 [01:03<00:29, 15.19it/s]
Volume Decoding:  69%|######9   | 987/1427 [01:03<00:29, 14.90it/s]
Volume Decoding:  69%|######9   | 989/1427 [01:03<00:28, 15.18it/s]
Volume Decoding:  69%|######9   | 991/1427 [01:03<00:29, 14.81it/s]
Volume Decoding:  70%|######9   | 993/1427 [01:03<00:28, 15.15it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  70%|######9   | 995/1427 [01:03<00:29, 14.74it/s]
Volume Decoding:  70%|######9   | 997/1427 [01:03<00:28, 15.03it/s]
Volume Decoding:  70%|#######   | 999/1427 [01:04<00:29, 14.73it/s]
Volume Decoding:  70%|#######   | 1001/1427 [01:04<00:28, 15.10it/s]
Volume Decoding:  70%|#######   | 1003/1427 [01:04<00:28, 14.88it/s]
Volume Decoding:  70%|#######   | 1005/1427 [01:04<00:27, 15.18it/s]
Volume Decoding:  71%|#######   | 1007/1427 [01:04<00:28, 14.88it/s]
Volume Decoding:  71%|#######   | 1009/1427 [01:04<00:27, 15.13it/s]
Volume Decoding:  71%|#######   | 1011/1427 [01:04<00:28, 14.80it/s]
Volume Decoding:  71%|#######   | 1013/1427 [01:04<00:27, 15.06it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  71%|#######1  | 1015/1427 [01:05<00:26, 15.33it/s]
Volume Decoding:  71%|#######1  | 1017/1427 [01:05<00:27, 14.97it/s]
Volume Decoding:  71%|#######1  | 1019/1427 [01:05<00:27, 14.74it/s]
Volume Decoding:  72%|#######1  | 1021/1427 [01:05<00:26, 15.12it/s]
Volume Decoding:  72%|#######1  | 1023/1427 [01:05<00:26, 15.30it/s]
Volume Decoding:  72%|#######1  | 1025/1427 [01:05<00:27, 14.83it/s]
Volume Decoding:  72%|#######1  | 1027/1427 [01:05<00:26, 15.09it/s]
Volume Decoding:  72%|#######2  | 1029/1427 [01:06<00:26, 14.79it/s]
Volume Decoding:  72%|#######2  | 1031/1427 [01:06<00:26, 15.10it/s]
Volume Decoding:  72%|#######2  | 1033/1427 [01:06<00:26, 14.85it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:52"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  73%|#######2  | 1035/1427 [01:06<00:25, 15.18it/s]
Volume Decoding:  73%|#######2  | 1037/1427 [01:06<00:26, 14.83it/s]
Volume Decoding:  73%|#######2  | 1039/1427 [01:06<00:25, 15.17it/s]
Volume Decoding:  73%|#######2  | 1041/1427 [01:06<00:26, 14.82it/s]
Volume Decoding:  73%|#######3  | 1043/1427 [01:06<00:25, 15.12it/s]
Volume Decoding:  73%|#######3  | 1045/1427 [01:07<00:25, 14.79it/s]
Volume Decoding:  73%|#######3  | 1047/1427 [01:07<00:25, 15.15it/s]
Volume Decoding:  74%|#######3  | 1049/1427 [01:07<00:25, 14.88it/s]
Volume Decoding:  74%|#######3  | 1051/1427 [01:07<00:24, 15.21it/s]
Volume Decoding:  74%|#######3  | 1053/1427 [01:07<00:25, 14.87it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  74%|#######3  | 1055/1427 [01:07<00:24, 15.25it/s]
Volume Decoding:  74%|#######4  | 1057/1427 [01:07<00:24, 14.91it/s]
Volume Decoding:  74%|#######4  | 1059/1427 [01:08<00:24, 15.15it/s]
Volume Decoding:  74%|#######4  | 1061/1427 [01:08<00:24, 14.78it/s]
Volume Decoding:  74%|#######4  | 1063/1427 [01:08<00:24, 15.07it/s]
Volume Decoding:  75%|#######4  | 1065/1427 [01:08<00:24, 14.72it/s]
Volume Decoding:  75%|#######4  | 1067/1427 [01:08<00:23, 15.05it/s]
Volume Decoding:  75%|#######4  | 1069/1427 [01:08<00:23, 15.21it/s]
Volume Decoding:  75%|#######5  | 1071/1427 [01:08<00:23, 14.87it/s]
Volume Decoding:  75%|#######5  | 1073/1427 [01:08<00:23, 15.16it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  75%|#######5  | 1075/1427 [01:09<00:23, 14.78it/s]
Volume Decoding:  75%|#######5  | 1077/1427 [01:09<00:23, 15.09it/s]
Volume Decoding:  76%|#######5  | 1079/1427 [01:09<00:22, 15.21it/s]
Volume Decoding:  76%|#######5  | 1081/1427 [01:09<00:23, 14.87it/s]
Volume Decoding:  76%|#######5  | 1083/1427 [01:09<00:22, 15.13it/s]
Volume Decoding:  76%|#######6  | 1085/1427 [01:09<00:23, 14.75it/s]
Volume Decoding:  76%|#######6  | 1087/1427 [01:09<00:22, 15.07it/s]
Volume Decoding:  76%|#######6  | 1089/1427 [01:10<00:22, 14.73it/s]
Volume Decoding:  76%|#######6  | 1091/1427 [01:10<00:22, 15.11it/s]
Volume Decoding:  77%|#######6  | 1093/1427 [01:10<00:22, 14.86it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  77%|#######6  | 1095/1427 [01:10<00:21, 15.19it/s]
Volume Decoding:  77%|#######6  | 1097/1427 [01:10<00:22, 14.78it/s]
Volume Decoding:  77%|#######7  | 1099/1427 [01:10<00:21, 15.12it/s]
Volume Decoding:  77%|#######7  | 1101/1427 [01:10<00:22, 14.76it/s]
Volume Decoding:  77%|#######7  | 1103/1427 [01:10<00:21, 15.04it/s]
Volume Decoding:  77%|#######7  | 1105/1427 [01:11<00:21, 15.28it/s]
Volume Decoding:  78%|#######7  | 1107/1427 [01:11<00:21, 14.97it/s]
Volume Decoding:  78%|#######7  | 1109/1427 [01:11<00:21, 14.76it/s]
Volume Decoding:  78%|#######7  | 1111/1427 [01:11<00:20, 15.10it/s]
Volume Decoding:  78%|#######7  | 1113/1427 [01:11<00:21, 14.80it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  78%|#######8  | 1115/1427 [01:11<00:20, 15.07it/s]
Volume Decoding:  78%|#######8  | 1117/1427 [01:11<00:20, 15.26it/s]
Volume Decoding:  78%|#######8  | 1119/1427 [01:12<00:20, 14.89it/s]
Volume Decoding:  79%|#######8  | 1121/1427 [01:12<00:20, 15.12it/s]
Volume Decoding:  79%|#######8  | 1123/1427 [01:12<00:20, 14.84it/s]
Volume Decoding:  79%|#######8  | 1125/1427 [01:12<00:19, 15.12it/s]
Volume Decoding:  79%|#######8  | 1127/1427 [01:12<00:20, 14.82it/s]
Volume Decoding:  79%|#######9  | 1129/1427 [01:12<00:19, 15.21it/s]
Volume Decoding:  79%|#######9  | 1131/1427 [01:12<00:19, 14.86it/s]
Volume Decoding:  79%|#######9  | 1133/1427 [01:12<00:19, 15.18it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:18:59"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  80%|#######9  | 1135/1427 [01:13<00:19, 14.83it/s]
Volume Decoding:  80%|#######9  | 1137/1427 [01:13<00:19, 15.14it/s]
Volume Decoding:  80%|#######9  | 1139/1427 [01:13<00:19, 14.80it/s]
Volume Decoding:  80%|#######9  | 1141/1427 [01:13<00:18, 15.09it/s]
Volume Decoding:  80%|########  | 1143/1427 [01:13<00:18, 15.25it/s]
Volume Decoding:  80%|########  | 1145/1427 [01:13<00:18, 14.88it/s]
Volume Decoding:  80%|########  | 1147/1427 [01:13<00:18, 15.19it/s]
Volume Decoding:  81%|########  | 1149/1427 [01:14<00:18, 14.84it/s]
Volume Decoding:  81%|########  | 1151/1427 [01:14<00:18, 15.06it/s]
Volume Decoding:  81%|########  | 1153/1427 [01:14<00:18, 14.84it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  81%|########  | 1155/1427 [01:14<00:17, 15.15it/s]
Volume Decoding:  81%|########1 | 1157/1427 [01:14<00:18, 14.80it/s]
Volume Decoding:  81%|########1 | 1159/1427 [01:14<00:17, 15.12it/s]
Volume Decoding:  81%|########1 | 1161/1427 [01:14<00:18, 14.77it/s]
Volume Decoding:  81%|########1 | 1163/1427 [01:14<00:17, 15.05it/s]
Volume Decoding:  82%|########1 | 1165/1427 [01:15<00:17, 14.83it/s]
Volume Decoding:  82%|########1 | 1167/1427 [01:15<00:17, 15.14it/s]
Volume Decoding:  82%|########1 | 1169/1427 [01:15<00:17, 14.82it/s]
Volume Decoding:  82%|########2 | 1171/1427 [01:15<00:16, 15.16it/s]
Volume Decoding:  82%|########2 | 1173/1427 [01:15<00:17, 14.80it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  82%|########2 | 1175/1427 [01:15<00:16, 15.04it/s]
Volume Decoding:  82%|########2 | 1177/1427 [01:15<00:16, 15.26it/s]
Volume Decoding:  83%|########2 | 1179/1427 [01:16<00:16, 14.85it/s]
Volume Decoding:  83%|########2 | 1181/1427 [01:16<00:16, 15.11it/s]
Volume Decoding:  83%|########2 | 1183/1427 [01:16<00:16, 14.84it/s]
Volume Decoding:  83%|########3 | 1185/1427 [01:16<00:16, 15.05it/s]
Volume Decoding:  83%|########3 | 1187/1427 [01:16<00:16, 14.76it/s]
Volume Decoding:  83%|########3 | 1189/1427 [01:16<00:15, 15.07it/s]
Volume Decoding:  83%|########3 | 1191/1427 [01:16<00:15, 15.29it/s]
Volume Decoding:  84%|########3 | 1193/1427 [01:16<00:15, 14.93it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  84%|########3 | 1195/1427 [01:17<00:15, 15.24it/s]
Volume Decoding:  84%|########3 | 1197/1427 [01:17<00:15, 14.84it/s]
Volume Decoding:  84%|########4 | 1199/1427 [01:17<00:15, 15.12it/s]
Volume Decoding:  84%|########4 | 1201/1427 [01:17<00:15, 14.74it/s]
Volume Decoding:  84%|########4 | 1203/1427 [01:17<00:14, 15.19it/s]
Volume Decoding:  84%|########4 | 1205/1427 [01:17<00:14, 14.83it/s]
Volume Decoding:  85%|########4 | 1207/1427 [01:17<00:14, 15.08it/s]
Volume Decoding:  85%|########4 | 1209/1427 [01:18<00:14, 14.80it/s]
Volume Decoding:  85%|########4 | 1211/1427 [01:18<00:14, 15.07it/s]
Volume Decoding:  85%|########5 | 1213/1427 [01:18<00:14, 14.74it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  85%|########5 | 1215/1427 [01:18<00:14, 15.02it/s]
Volume Decoding:  85%|########5 | 1217/1427 [01:18<00:13, 15.26it/s]
Volume Decoding:  85%|########5 | 1219/1427 [01:18<00:14, 14.81it/s]
Volume Decoding:  86%|########5 | 1221/1427 [01:18<00:13, 15.17it/s]
Volume Decoding:  86%|########5 | 1223/1427 [01:18<00:13, 14.81it/s]
Volume Decoding:  86%|########5 | 1225/1427 [01:19<00:13, 15.08it/s]
Volume Decoding:  86%|########5 | 1227/1427 [01:19<00:13, 14.80it/s]
Volume Decoding:  86%|########6 | 1229/1427 [01:19<00:13, 15.10it/s]
Volume Decoding:  86%|########6 | 1231/1427 [01:19<00:13, 14.74it/s]
Volume Decoding:  86%|########6 | 1233/1427 [01:19<00:12, 15.12it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:19:05"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  87%|########6 | 1235/1427 [01:19<00:12, 14.81it/s]
Volume Decoding:  87%|########6 | 1237/1427 [01:19<00:12, 15.13it/s]
Volume Decoding:  87%|########6 | 1239/1427 [01:20<00:12, 14.87it/s]
Volume Decoding:  87%|########6 | 1241/1427 [01:20<00:12, 15.14it/s]
Volume Decoding:  87%|########7 | 1243/1427 [01:20<00:12, 14.81it/s]
Volume Decoding:  87%|########7 | 1245/1427 [01:20<00:12, 15.14it/s]
Volume Decoding:  87%|########7 | 1247/1427 [01:20<00:12, 14.82it/s]
Volume Decoding:  88%|########7 | 1249/1427 [01:20<00:11, 15.06it/s]
Volume Decoding:  88%|########7 | 1251/1427 [01:20<00:11, 15.26it/s]
Volume Decoding:  88%|########7 | 1253/1427 [01:20<00:11, 14.83it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  88%|########7 | 1255/1427 [01:21<00:11, 15.18it/s]
Volume Decoding:  88%|########8 | 1257/1427 [01:21<00:11, 14.84it/s]
Volume Decoding:  88%|########8 | 1259/1427 [01:21<00:11, 15.18it/s]
Volume Decoding:  88%|########8 | 1261/1427 [01:21<00:11, 14.84it/s]
Volume Decoding:  89%|########8 | 1263/1427 [01:21<00:10, 15.11it/s]
Volume Decoding:  89%|########8 | 1265/1427 [01:21<00:10, 14.79it/s]
Volume Decoding:  89%|########8 | 1267/1427 [01:21<00:10, 15.07it/s]
Volume Decoding:  89%|########8 | 1269/1427 [01:22<00:10, 14.73it/s]
Volume Decoding:  89%|########9 | 1271/1427 [01:22<00:10, 14.99it/s]
Volume Decoding:  89%|########9 | 1273/1427 [01:22<00:10, 15.24it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  89%|########9 | 1275/1427 [01:22<00:10, 14.88it/s]
Volume Decoding:  89%|########9 | 1277/1427 [01:22<00:09, 15.25it/s]
Volume Decoding:  90%|########9 | 1279/1427 [01:22<00:09, 14.86it/s]
Volume Decoding:  90%|########9 | 1281/1427 [01:22<00:09, 15.08it/s]
Volume Decoding:  90%|########9 | 1283/1427 [01:22<00:09, 14.74it/s]
Volume Decoding:  90%|######### | 1285/1427 [01:23<00:09, 14.99it/s]
Volume Decoding:  90%|######### | 1287/1427 [01:23<00:09, 15.21it/s]
Volume Decoding:  90%|######### | 1289/1427 [01:23<00:09, 14.80it/s]
Volume Decoding:  90%|######### | 1291/1427 [01:23<00:08, 15.13it/s]
Volume Decoding:  91%|######### | 1293/1427 [01:23<00:09, 14.80it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  91%|######### | 1295/1427 [01:23<00:08, 15.12it/s]
Volume Decoding:  91%|######### | 1297/1427 [01:23<00:08, 14.78it/s]
Volume Decoding:  91%|#########1| 1299/1427 [01:24<00:08, 15.04it/s]
Volume Decoding:  91%|#########1| 1301/1427 [01:24<00:08, 14.72it/s]
Volume Decoding:  91%|#########1| 1303/1427 [01:24<00:08, 15.01it/s]
Volume Decoding:  91%|#########1| 1305/1427 [01:24<00:07, 15.28it/s]
Volume Decoding:  92%|#########1| 1307/1427 [01:24<00:08, 14.80it/s]
Volume Decoding:  92%|#########1| 1309/1427 [01:24<00:07, 15.09it/s]
Volume Decoding:  92%|#########1| 1311/1427 [01:24<00:07, 14.82it/s]
Volume Decoding:  92%|#########2| 1313/1427 [01:24<00:07, 15.20it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  92%|#########2| 1315/1427 [01:25<00:07, 14.93it/s]
Volume Decoding:  92%|#########2| 1317/1427 [01:25<00:07, 15.13it/s]
Volume Decoding:  92%|#########2| 1319/1427 [01:25<00:07, 14.80it/s]
Volume Decoding:  93%|#########2| 1321/1427 [01:25<00:07, 15.10it/s]
Volume Decoding:  93%|#########2| 1323/1427 [01:25<00:07, 14.75it/s]
Volume Decoding:  93%|#########2| 1325/1427 [01:25<00:06, 15.12it/s]
Volume Decoding:  93%|#########2| 1327/1427 [01:25<00:06, 14.77it/s]
Volume Decoding:  93%|#########3| 1329/1427 [01:26<00:06, 14.98it/s]
Volume Decoding:  93%|#########3| 1331/1427 [01:26<00:06, 15.27it/s]
Volume Decoding:  93%|#########3| 1333/1427 [01:26<00:06, 14.86it/s]
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:19:12"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  94%|#########3| 1335/1427 [01:26<00:06, 15.17it/s]
Volume Decoding:  94%|#########3| 1337/1427 [01:26<00:06, 14.82it/s]
Volume Decoding:  94%|#########3| 1339/1427 [01:26<00:05, 15.07it/s]
Volume Decoding:  94%|#########3| 1341/1427 [01:26<00:05, 14.85it/s]
Volume Decoding:  94%|#########4| 1343/1427 [01:26<00:05, 15.13it/s]
Volume Decoding:  94%|#########4| 1345/1427 [01:27<00:05, 14.84it/s]
Volume Decoding:  94%|#########4| 1347/1427 [01:27<00:05, 15.16it/s]
Volume Decoding:  95%|#########4| 1349/1427 [01:27<00:05, 14.79it/s]
Volume Decoding:  95%|#########4| 1351/1427 [01:27<00:04, 15.24it/s]
Volume Decoding:  95%|#########4| 1353/1427 [01:27<00:04, 14.90it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  95%|#########4| 1355/1427 [01:27<00:04, 15.19it/s]
Volume Decoding:  95%|#########5| 1357/1427 [01:27<00:04, 14.84it/s]
Volume Decoding:  95%|#########5| 1359/1427 [01:28<00:04, 15.16it/s]
Volume Decoding:  95%|#########5| 1361/1427 [01:28<00:04, 14.84it/s]
Volume Decoding:  96%|#########5| 1363/1427 [01:28<00:04, 15.06it/s]
Volume Decoding:  96%|#########5| 1365/1427 [01:28<00:04, 14.78it/s]
Volume Decoding:  96%|#########5| 1367/1427 [01:28<00:03, 15.05it/s]
Volume Decoding:  96%|#########5| 1369/1427 [01:28<00:03, 15.21it/s]
Volume Decoding:  96%|#########6| 1371/1427 [01:28<00:03, 14.87it/s]
Volume Decoding:  96%|#########6| 1373/1427 [01:28<00:03, 15.17it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  96%|#########6| 1375/1427 [01:29<00:03, 14.86it/s]
Volume Decoding:  96%|#########6| 1377/1427 [01:29<00:03, 15.11it/s]
Volume Decoding:  97%|#########6| 1379/1427 [01:29<00:03, 14.78it/s]
Volume Decoding:  97%|#########6| 1381/1427 [01:29<00:03, 15.05it/s]
Volume Decoding:  97%|#########6| 1383/1427 [01:29<00:02, 14.75it/s]
Volume Decoding:  97%|#########7| 1385/1427 [01:29<00:02, 15.03it/s]
Volume Decoding:  97%|#########7| 1387/1427 [01:29<00:02, 15.25it/s]
Volume Decoding:  97%|#########7| 1389/1427 [01:30<00:02, 14.99it/s]
Volume Decoding:  97%|#########7| 1391/1427 [01:30<00:02, 15.23it/s]
Volume Decoding:  98%|#########7| 1393/1427 [01:30<00:02, 14.83it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  98%|#########7| 1395/1427 [01:30<00:02, 15.07it/s]
Volume Decoding:  98%|#########7| 1397/1427 [01:30<00:02, 14.76it/s]
Volume Decoding:  98%|#########8| 1399/1427 [01:30<00:01, 15.06it/s]
Volume Decoding:  98%|#########8| 1401/1427 [01:30<00:01, 14.81it/s]
Volume Decoding:  98%|#########8| 1403/1427 [01:30<00:01, 15.19it/s]
Volume Decoding:  98%|#########8| 1405/1427 [01:31<00:01, 14.83it/s]
Volume Decoding:  99%|#########8| 1407/1427 [01:31<00:01, 15.26it/s]
Volume Decoding:  99%|#########8| 1409/1427 [01:31<00:01, 14.92it/s]
Volume Decoding:  99%|#########8| 1411/1427 [01:31<00:01, 14.68it/s]
Volume Decoding:  99%|#########9| 1413/1427 [01:31<00:00, 14.96it/s]
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  99%|#########9| 1415/1427 [01:31<00:00, 15.22it/s]
Volume Decoding:  99%|#########9| 1417/1427 [01:31<00:00, 14.85it/s]
Volume Decoding:  99%|#########9| 1419/1427 [01:32<00:00, 15.11it/s]
Volume Decoding: 100%|#########9| 1421/1427 [01:32<00:00, 14.79it/s]
Volume Decoding: 100%|#########9| 1423/1427 [01:32<00:00, 15.06it/s]
Volume Decoding: 100%|#########9| 1425/1427 [01:32<00:00, 14.87it/s]
Volume Decoding: 100%|##########| 1427/1427 [01:32<00:00, 15.41it/s]

[HunyaunServer] [STDOUT] 2025-07-09 17:19:26 | INFO | hunyuan3d_api | Processing mesh (faces: 613060)
info: [Hunyaun Progress] hunyaun_generation: 0% - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-09 17:19:29"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
[HunyaunServer] [STDOUT] 2025-07-09 17:19:29 | INFO | hunyuan3d_api | Reducing faces to 582407
[HunyaunServer] [STDOUT] 2025-07-09 17:19:32 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
[HunyaunServer] [STDOUT] 2025-07-09 17:19:35 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
[HunyaunServer] [STDOUT] 2025-07-09 17:19:35 | INFO | hunyuan3d_api | Texture memory optimization applied