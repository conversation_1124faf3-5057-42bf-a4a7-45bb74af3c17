========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2111 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-Cx54nlL0.css     34.87 kB │ gzip:   6.16 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-DpOcXYbZ.js   1,267.86 kB │ gzip: 349.82 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 12.74s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
[Trellis Server] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:38:35"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:38:35"}
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-09 16:38:36"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 16:38:37"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-09 16:38:37"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-09 16:38:37"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-09 16:38:37"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-09 16:38:37"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:38:38"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-09 16:38:40"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-09 16:38:40"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-09 16:38:40"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-09 16:38:43"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-09 16:38:43"}
info: [upload-file] Received ΓÇô filename: 20250613_Wood_Chair_With_Brown_Cussions.jpg, size: 989189 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-09 16:40:28"}
info: Buffer received (bytes): 989189 {"service":"user-service","timestamp":"2025-07-09 16:40:28"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\08eaf6a6-feef-4f71-b5d2-04fae24c65a1\20250613_Wood_Chair_With_Brown_Cussions.jpg {"service":"user-service","timestamp":"2025-07-09 16:40:28"}
info: Starting background removal for N:\3D AI Studio\uploads\08eaf6a6-feef-4f71-b5d2-04fae24c65a1\20250613_Wood_Chair_With_Brown_Cussions.jpg... {"service":"user-service","timestamp":"2025-07-09 16:40:28"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\08eaf6a6-feef-4f71-b5d2-04fae24c65a1\20250613_Wood_Chair_With_Brown_Cussions.jpg N:\3D AI Studio\uploads\08eaf6a6-feef-4f71-b5d2-04fae24c65a1\20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-09 16:40:28"}
info: [load-file] Received request for: uploads/08eaf6a6-feef-4f71-b5d2-04fae24c65a1/20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-09 16:40:37"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\08eaf6a6-feef-4f71-b5d2-04fae24c65a1\20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-09 16:40:37"}
info: IPC: run-pipeline called for: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:40:47"}
info: runPipeline request: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:40:47"}
info: runPipeline: Registered pipelines at call time: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-09 16:40:47"}
info: Checking dependencies for Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:40:47"}
warn: Python dependencies not satisfied for Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-09 16:40:47"}
[IPC Handler] Sending status: hunyaun - 25% - Launching Hunyaun server and generating 3D model...
[HunyaunServer] generate3DModel called with imagePath: uploads/08eaf6a6-feef-4f71-b5d2-04fae24c65a1/20250613_Wood_Chair_With_Brown_Cussions_processed.png
[HunyaunServer] Settings received: {
  ss_steps: 12,
  ss_cfg_strength: 7.5,
  slat_steps: 12,
  slat_cfg_strength: 3,
  randomize_seed: true,
  seed: 330145,
  simplify: 0.95,
  texture_size: 1024,
  enable_lighting_optimizer: true,
  octree_resolution: 128,
  num_inference_steps: 5,
  guidance_scale: 5,
  enable_texture: true,
  face_count: 40000
}
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] isHunyaunRunning() returned: false
[HunyaunServer] [Hunyaun Progress] Stage tracking reset for new generation
[HunyaunServer] Server not running, starting server...
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] startHunyaunServer() called successfully
[HunyaunServer] Waiting for Hunyaun server to be ready...
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] Creating virtual environment using portable Python...
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe" -m virtualenv "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] sitecustomize.py applied
[HunyaunServer] [STDOUT]         1 file(s) copied.
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 16:40:56"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (10 attempts): ECONNREFUSED
[HunyaunServer] Connection check #10
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-09 16:41:16,911 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 16:41:16,912 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[HunyaunServer] [STDOUT] 2025-07-09 16:41:16 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
[HunyaunServer] Server not running - connection failed (20 attempts): ECONNREFUSED
[HunyaunServer] Connection check #20
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
Fetching 3 files: 100%|##########| 3/3 [00:00<00:00, 2895.95it/s]

[HunyaunServer] [STDOUT] 2025-07-09 16:41:29 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[HunyaunServer] [STDERR] 2025-07-09 16:41:29,823 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 16:41:36"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (30 attempts): ECONNREFUSED
[HunyaunServer] Connection check #30
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] Server not running - connection failed (40 attempts): ECONNREFUSED
[HunyaunServer] Connection check #40
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 2160.21it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 2822.88it/s]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
Loading pipeline components...:  33%|###3      | 2/6 [00:03<00:06,  1.64s/it]
Loading pipeline components...:  50%|#####     | 3/6 [00:03<00:03,  1.09s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:03<00:00,  1.54it/s]

[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[HunyaunServer] Server not running - connection failed (50 attempts): ECONNREFUSED
[HunyaunServer] Connection check #50
info: [Hunyaun Progress] hunyaun: 0% - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-09 16:42:27"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  33%|###3      | 2/6 [00:16<00:32,  8.01s/it]
Loading pipeline components...:  50%|#####     | 3/6 [00:20<00:18,  6.33s/it]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...: 100%|##########| 6/6 [00:20<00:00,  3.47s/it]

[HunyaunServer] [STDOUT] 2025-07-09 16:42:38 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] ==================================================
[HunyaunServer] [STDOUT] Server is active and listening on 127.0.0.1:7960
[HunyaunServer] Server is running - connection successful
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] Sending generation request to Hunyaun server...
[HunyaunServer] [STDOUT] ==================================================
[HunyaunServer] [STDOUT] 2025-07-09 16:42:40 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] 2025-07-09 16:42:40 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
[HunyaunServer] [STDOUT] 2025-07-09 16:42:42 | INFO | hunyuan3d_api | Shape generation model moved to cuda
Diffusion Sampling::   0%|          | 0/5 [00:00<?, ?it/s]
Diffusion Sampling::  20%|##        | 1/5 [00:01<00:04,  1.19s/it]
Diffusion Sampling::  40%|####      | 2/5 [00:01<00:02,  1.43it/s]
Diffusion Sampling::  60%|######    | 3/5 [00:02<00:01,  1.17it/s]
Diffusion Sampling::  80%|########  | 4/5 [00:03<00:00,  1.09it/s]
Diffusion Sampling:: 100%|##########| 5/5 [00:04<00:00,  1.08it/s]

Volume Decoding:   0%|          | 0/27 [00:00<?, ?it/s]
Volume Decoding:   4%|3         | 1/27 [00:00<00:04,  5.31it/s]
Volume Decoding: 100%|##########| 27/27 [00:00<00:00, 103.27it/s]

[HunyaunServer] [STDOUT] 2025-07-09 16:42:59 | INFO | hunyuan3d_api | Processing mesh (faces: 485340)
[HunyaunServer] [STDOUT] 2025-07-09 16:43:02 | INFO | hunyuan3d_api | Reducing faces to 461073
[HunyaunServer] [STDOUT] 2025-07-09 16:43:05 | INFO | hunyuan3d_api | Skipping texture generation for this request
[HunyaunServer] [STDOUT] 2025-07-09 16:43:05 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 5.57 seconds
[HunyaunServer] [STDOUT] 2025-07-09 16:43:05 | INFO | hunyuan3d_api | Generation completed in 26.05 seconds
[HunyaunServer] Generation request sent successfully
info: [Hunyaun Progress] hunyaun: 100% - Generation complete {"service":"user-service","timestamp":"2025-07-09 16:43:05"}
[IPC Handler] Sending status: hunyaun - 100% - Generation complete
[HunyaunServer] Downloading generated model...
[HunyaunServer] [STDOUT] 2025-07-09 16:43:05 | INFO | hunyuan3d_api | Client is downloading a model.
[HunyaunServer] Model saved to: N:\3D AI Studio\output\hunyaun_model_2025-07-09T21-43-05-610Z.glb
info: [Hunyaun Progress] hunyaun_export: 100% - GLB Export: Finalizing 3D model file {"service":"user-service","timestamp":"2025-07-09 16:43:06"}
[IPC Handler] Sending status: hunyaun_export - 100% - GLB Export: Finalizing 3D model file
info: [load-file] Received request for: output\hunyaun_model_2025-07-09T21-43-05-610Z.glb {"service":"user-service","timestamp":"2025-07-09 16:43:06"}
info: [load-file] Reading absolute path: N:\3D AI Studio\output\hunyaun_model_2025-07-09T21-43-05-610Z.glb {"service":"user-service","timestamp":"2025-07-09 16:43:06"}
warn: [load-file] Received invalid request for: undefined {"service":"user-service","timestamp":"2025-07-09 16:43:06"}
info: All windows closed. {"service":"user-service","timestamp":"2025-07-09 16:43:53"}
info: Quitting app. {"service":"user-service","timestamp":"2025-07-09 16:43:53"}
Press any key to continue . . .