import React, { useState } from 'react';
import { <PERSON><PERSON>ircle, Circle, Loader2, <PERSON>, Zap, Cpu, Download, ChevronDown, ChevronUp, Type, Sparkles, Image as ImageIcon } from 'lucide-react';

interface ProgressStage {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  status: 'pending' | 'active' | 'completed' | 'error';
  estimatedTime?: string;
}

interface ImageGenerationProgressBarProps {
  isDarkMode: boolean;
  currentStage: string;
  progress: number;
  progressText: string;
  currentStep: number;
  totalSteps: number;
  stageProgress: number;
}

const ImageGenerationProgressBar: React.FC<ImageGenerationProgressBarProps> = ({
  isDarkMode,
  currentStage,
  progress,
  progressText,
  currentStep,
  totalSteps,
  stageProgress
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Define image generation stages
  const getStages = (): ProgressStage[] => {
    const stages: ProgressStage[] = [
      {
        id: 'initializing',
        name: 'Initializing',
        description: 'Setting up generation parameters',
        icon: <Zap className="w-4 h-4" />,
        progress: 0,
        status: 'pending',
        estimatedTime: '2s'
      },
      {
        id: 'loading_model',
        name: 'Loading Model',
        description: 'Loading AI model and preparing pipeline',
        icon: <Download className="w-4 h-4" />,
        progress: 0,
        status: 'pending',
        estimatedTime: '10s'
      },
      {
        id: 'generating',
        name: 'Generating Image',
        description: `Denoising process (${currentStep}/${totalSteps} steps)`,
        icon: <Sparkles className="w-4 h-4" />,
        progress: 0,
        status: 'pending',
        estimatedTime: '15s'
      },
      {
        id: 'saving',
        name: 'Finalizing',
        description: 'Saving and processing final image',
        icon: <CheckCircle className="w-4 h-4" />,
        progress: 0,
        status: 'pending',
        estimatedTime: '2s'
      }
    ];

    // Update stage status and progress based on current stage
    stages.forEach((stage, index) => {
      if (stage.id === currentStage) {
        stage.status = 'active';
        stage.progress = currentStage === 'generating' ? stageProgress : progress;
      } else if (stages.findIndex(s => s.id === currentStage) > index) {
        stage.status = 'completed';
        stage.progress = 100;
      } else {
        stage.status = 'pending';
        stage.progress = 0;
      }
    });

    return stages;
  };

  const stages = getStages();
  const currentStageName = stages.find(s => s.status === 'active')?.name || 'Preparing...';

  return (
    <div className={`relative w-full rounded-lg border ${
      isDarkMode
        ? 'bg-gray-800 border-gray-700'
        : 'bg-white border-gray-200'
    } shadow-lg overflow-visible`}>
      {/* Compact Header - Always Visible */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Loader2 className={`w-5 h-5 animate-spin ${
              isDarkMode ? 'text-blue-400' : 'text-blue-600'
            }`} />
            <div className="flex flex-col">
              <h3 className={`text-sm font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Generating Image
              </h3>
              <p className={`text-xs ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                {currentStageName}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className={`text-right ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <div className="text-sm font-semibold">{progress}%</div>
              <div className="text-xs text-gray-500">Complete</div>
            </div>
            
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={`p-1 rounded-md transition-colors ${
                isDarkMode
                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-300'
                  : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
              }`}
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>

        {/* Overall Progress Bar */}
        <div className={`mt-3 w-full h-2 rounded-full ${
          isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
        }`}>
          <div
            className={`h-full rounded-full transition-all duration-500 ${
              isDarkMode ? 'bg-blue-500' : 'bg-blue-600'
            }`}
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Expanded Details */}
      {isExpanded && (
        <div className={`border-t px-4 pb-4 ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="space-y-3 mt-4">
            {stages.map((stage, index) => (
              <div key={stage.id} className="flex items-center gap-3">
                {/* Stage Icon */}
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  stage.status === 'completed'
                    ? isDarkMode ? 'bg-green-900 text-green-400' : 'bg-green-100 text-green-600'
                    : stage.status === 'active'
                    ? isDarkMode ? 'bg-blue-900 text-blue-400' : 'bg-blue-100 text-blue-600'
                    : isDarkMode ? 'bg-gray-700 text-gray-500' : 'bg-gray-100 text-gray-400'
                }`}>
                  {stage.status === 'completed' ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : stage.status === 'active' ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    stage.icon
                  )}
                </div>

                {/* Stage Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      <span className={`text-sm font-medium ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-800'
                      }`}>
                        {stage.name}
                      </span>
                      <span className={`text-xs ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {stage.description}
                      </span>
                    </div>
                    
                    <div className={`text-xs font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>
                      {stage.status === 'active' ? `${Math.round(stage.progress)}%` : 
                       stage.status === 'completed' ? '✓' : stage.estimatedTime}
                    </div>
                  </div>

                  {/* Stage Progress Bar */}
                  {stage.status === 'active' && (
                    <div className={`mt-2 w-full h-1.5 rounded-full ${
                      isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                    }`}>
                      <div
                        className={`h-full rounded-full transition-all duration-200 ${
                          isDarkMode ? 'bg-blue-500' : 'bg-blue-600'
                        }`}
                        style={{ width: `${stage.progress}%` }}
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Additional Info */}
          <div className={`mt-4 pt-3 border-t text-center text-xs ${
            isDarkMode ? 'border-gray-700 text-gray-400' : 'border-gray-200 text-gray-500'
          }`}>
            {progressText || 'Processing...'}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGenerationProgressBar;
