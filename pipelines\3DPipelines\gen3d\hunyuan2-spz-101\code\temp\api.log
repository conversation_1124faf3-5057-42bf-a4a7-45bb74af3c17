2025-07-09 13:42:05 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 13:42:05 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 13:54:41 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 14:00:34 | WARNING | huggingface_hub.file_download | Error while downloading from https://cdn-lfs-us-1.hf.co/repos/a9/84/a9845b44053f70565d98bcc8256a3eb847a45054fd0206fd1d30a60c4a3aab98/0ce61d15a43d11ba19079ab8f24dfce78b876d3f5291470079ef64b17e08ca58?response-content-disposition=inline%3B+filename*%3DUTF-8%27%27diffusion_pytorch_model.safetensors%3B+filename%3D%22diffusion_pytorch_model.safetensors%22%3B&Expires=1752090931&Policy=eyJTdGF0ZW1lbnQiOlt7IkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1MjA5MDkzMX19LCJSZXNvdXJjZSI6Imh0dHBzOi8vY2RuLWxmcy11cy0xLmhmLmNvL3JlcG9zL2E5Lzg0L2E5ODQ1YjQ0MDUzZjcwNTY1ZDk4YmNjODI1NmEzZWI4NDdhNDUwNTRmZDAyMDZmZDFkMzBhNjBjNGEzYWFiOTgvMGNlNjFkMTVhNDNkMTFiYTE5MDc5YWI4ZjI0ZGZjZTc4Yjg3NmQzZjUyOTE0NzAwNzllZjY0YjE3ZTA4Y2E1OD9yZXNwb25zZS1jb250ZW50LWRpc3Bvc2l0aW9uPSoifV19&Signature=QeKBQVe64-UV4YjgKXEtRMD68sW56SmGvj0cTgyz6y3ptm1gv1TPeA2haDY7fov5lZHzDAZ1OuDqDo0DPiTOURGGRdMcvLr2N6YAXF7GuOkGTmwE-ubh017RJ7Wspwy3ZMwvwJPTNUoR0Lj1q4XUPB7to3XW9fWbyHcQX7d2-FS6n-IxKlZ04H1mzkpB8RMuqaroSNQvka7x7hd-2a1yZX2XvSiy8JLrucNyXjVjVlLEdnyXjIam0BKEIVknfxKHzqtTFx2uxXDBNTg4v9R9Ojo7q7RnKWTb-7L11-p3dsftoYglXx5uVCU1QGTR%7EGxQkvg-WZfJBwf7FnNVxlC3mw__&Key-Pair-Id=K24J24Z295AEI9: [SSL: DECRYPTION_FAILED_OR_BAD_RECORD_MAC] decryption failed or bad record mac (_ssl.c:2580)
Trying to resume download...
2025-07-09 14:13:31 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 14:13:31 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 14:13:31 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 14:13:31 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 14:19:36 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 14:19:36 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 14:19:49 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 14:21:11 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 14:21:11 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 14:21:11 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 14:21:11 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 14:50:03 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 14:50:03 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 14:50:15 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 14:51:30 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 14:51:30 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 14:51:30 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 14:51:30 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 14:52:48 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 14:52:48 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 14:53:01 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 14:54:13 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 14:54:13 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 14:54:13 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 14:54:13 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 15:55:19 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 15:55:19 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 15:55:32 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 15:56:42 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 15:56:42 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 15:56:42 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 15:56:42 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 15:56:43 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 15:56:43 | ERROR | hunyuan3d_api | Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\api_spz\routes\generation.py", line 439, in generate_multi_no_preview
    _gen_3d_validate_params(file_list, image_list_base64, arg)
  File "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\api_spz\routes\generation.py", line 96, in _gen_3d_validate_params
    raise HTTPException(status_code=400, detail="Number of chunks must be between 1000 and 200000")
fastapi.exceptions.HTTPException: 400: Number of chunks must be between 1000 and 200000

2025-07-09 15:56:43 | ERROR | hunyuan3d_api | Error processing UI generation request: 500: 400: Number of chunks must be between 1000 and 200000
2025-07-09 16:06:12 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 16:06:12 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 16:06:25 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 16:07:31 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 16:07:31 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 16:07:31 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 16:07:31 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 16:07:33 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 16:07:34 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 16:07:34 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 16:07:36 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 16:08:35 | INFO | hunyuan3d_api | Processing mesh (faces: 253842)
2025-07-09 16:08:37 | INFO | hunyuan3d_api | Reducing faces to 241149
2025-07-09 16:08:38 | INFO | hunyuan3d_api | Skipping texture generation for this request
2025-07-09 16:08:38 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 2.58 seconds
2025-07-09 16:08:38 | INFO | hunyuan3d_api | Generation completed in 64.88 seconds
2025-07-09 16:08:38 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-09 16:33:59 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 16:33:59 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 16:34:11 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 16:35:17 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 16:35:17 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 16:35:17 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 16:35:17 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 16:35:19 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 16:35:20 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 16:35:20 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 16:35:22 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 16:35:42 | INFO | hunyuan3d_api | Processing mesh (faces: 378780)
2025-07-09 16:35:44 | INFO | hunyuan3d_api | Reducing faces to 359841
2025-07-09 16:35:46 | INFO | hunyuan3d_api | Skipping texture generation for this request
2025-07-09 16:35:46 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 4.31 seconds
2025-07-09 16:35:46 | INFO | hunyuan3d_api | Generation completed in 27.32 seconds
2025-07-09 16:35:46 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-09 16:41:16 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 16:41:16 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 16:41:29 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 16:42:38 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 16:42:38 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 16:42:38 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 16:42:38 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 16:42:39 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 16:42:40 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 16:42:40 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 16:42:42 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 16:42:59 | INFO | hunyuan3d_api | Processing mesh (faces: 485340)
2025-07-09 16:43:02 | INFO | hunyuan3d_api | Reducing faces to 461073
2025-07-09 16:43:05 | INFO | hunyuan3d_api | Skipping texture generation for this request
2025-07-09 16:43:05 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 5.57 seconds
2025-07-09 16:43:05 | INFO | hunyuan3d_api | Generation completed in 26.05 seconds
2025-07-09 16:43:05 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-09 16:48:33 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 16:48:33 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 16:48:46 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 16:49:54 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 16:49:54 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 16:49:54 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 16:49:54 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 16:49:55 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 16:49:55 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 16:49:55 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 16:49:58 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 16:50:16 | INFO | hunyuan3d_api | Processing mesh (faces: 53888)
2025-07-09 16:50:16 | INFO | hunyuan3d_api | Reducing faces to 51193
2025-07-09 16:50:17 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-09 16:50:20 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-09 16:50:20 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-09 16:50:20 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-09 16:50:20 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-09 16:50:20 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-09 16:50:49 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 16:50:50 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 16:50:51 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 16:51:35 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-09 16:51:35 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-09 16:51:45 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-09 16:51:46 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 90.45 seconds
2025-07-09 16:51:46 | INFO | hunyuan3d_api | Generation completed in 111.33 seconds
2025-07-09 16:51:46 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-09 17:05:25 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 17:05:25 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 17:05:38 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 17:06:48 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 17:06:48 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 17:06:48 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 17:06:48 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 17:06:50 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 17:06:50 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 17:06:50 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 17:06:53 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 17:09:16 | INFO | hunyuan3d_api | Processing mesh (faces: 623624)
2025-07-09 17:09:19 | INFO | hunyuan3d_api | Reducing faces to 592442
2025-07-09 17:09:23 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-09 17:09:25 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-09 17:09:25 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-09 17:09:25 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-09 17:09:25 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-09 17:09:25 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-09 17:15:44 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 17:15:44 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 17:15:56 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 17:17:08 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 17:17:08 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 17:17:08 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 17:17:08 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 17:17:09 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 17:17:09 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 17:17:09 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 17:17:11 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 17:19:26 | INFO | hunyuan3d_api | Processing mesh (faces: 613060)
2025-07-09 17:19:29 | INFO | hunyuan3d_api | Reducing faces to 582407
2025-07-09 17:19:32 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-09 17:19:35 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-09 17:19:35 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-09 17:19:35 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-09 17:19:35 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-09 17:19:35 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-09 17:26:50 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 17:26:50 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 17:27:03 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 17:28:12 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 17:28:12 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 17:28:12 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 17:28:12 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 17:28:12 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 17:28:13 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 17:28:13 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 17:28:16 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 17:29:15 | INFO | hunyuan3d_api | Processing mesh (faces: 237492)
2025-07-09 17:29:16 | INFO | hunyuan3d_api | Reducing faces to 225617
2025-07-09 17:29:18 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-09 17:29:20 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-09 17:29:20 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-09 17:29:20 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-09 17:29:20 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-09 17:29:20 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-09 17:31:49 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 17:31:50 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 17:31:51 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 17:32:35 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-09 17:32:35 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-09 17:32:44 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-09 17:32:46 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 211.12 seconds
2025-07-09 17:32:46 | INFO | hunyuan3d_api | Generation completed in 274.34 seconds
2025-07-09 17:32:46 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-09 17:46:27 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 17:46:27 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 17:46:40 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 17:47:53 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 17:47:53 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 17:47:53 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 17:47:53 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 17:47:55 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 17:47:55 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 17:47:55 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 17:47:59 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 17:53:13 | INFO | hunyuan3d_api | Processing mesh (faces: 3138238)
2025-07-09 17:53:28 | INFO | hunyuan3d_api | Reducing faces to 2981326
2025-07-09 17:53:45 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-09 17:53:47 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-09 17:53:47 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-09 17:53:47 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-09 17:53:47 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-09 17:53:47 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-09 18:18:37 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-09 18:18:37 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-09 18:18:50 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-09 18:20:03 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-09 18:20:03 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-09 18:20:03 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-09 18:20:03 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-09 18:20:05 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-09 18:20:05 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-09 18:20:05 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-09 18:20:08 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-09 18:22:32 | INFO | hunyuan3d_api | Processing mesh (faces: 873428)
2025-07-09 18:22:37 | INFO | hunyuan3d_api | Reducing faces to 829756
2025-07-09 18:22:42 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-09 18:22:44 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-09 18:22:44 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-09 18:22:44 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-09 18:22:44 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-09 18:22:44 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-09 18:37:08 | ERROR | asyncio | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "asyncio\events.py", line 84, in _run
  File "asyncio\proactor_events.py", line 165, in _call_connection_lost
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-07-09 18:37:12 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 18:37:12 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 18:37:13 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-09 18:37:57 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-09 18:37:57 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-09 18:38:10 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-09 18:38:12 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 939.50 seconds
2025-07-09 18:38:12 | INFO | hunyuan3d_api | Generation completed in 1087.32 seconds
