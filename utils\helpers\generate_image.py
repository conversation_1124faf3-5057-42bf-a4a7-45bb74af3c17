#!/usr/bin/env python3
"""
Advanced Image Generation Script for 3D AI Studio
Supports multiple models including FLUX Dev, SDXL, and Stable Diffusion variants
Application-level utility stored in utils/helpers/ for portability
"""

import argparse
import json
import os
import sys
import time
import traceback
import base64
import io
from pathlib import Path
from typing import Dict, Any, Optional, <PERSON>ple
import re
from datetime import datetime

import torch
import numpy as np
from PIL import Image
from diffusers import (  # type: ignore
    StableDiffusionPipeline,
    StableDiffusionXLPipeline, 
    StableDiffusionXLImg2ImgPipeline,
    FluxPipeline,
    DiffusionPipeline,
    AutoencoderKL,
    EulerDiscreteScheduler,
    DPMSolverMultistepScheduler,
    LMSDiscreteScheduler,
    PNDMScheduler,
    DDIMScheduler,
    EulerAncestralDiscreteScheduler,
    UniPCMultistepScheduler
)
from diffusers.utils import logging as diffusers_logging, load_image  # type: ignore

# Disable diffusers warnings for cleaner output
diffusers_logging.set_verbosity_error()

# Add the project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import quantized FLUX components
from utils.helpers.flux_quantized_model import (
    create_enhanced_quantized_flux_pipeline,
    create_flux_with_callback_support,
    get_memory_usage,
    optimize_pipeline_memory
)
FLUX_QUANTIZED_AVAILABLE = True

class ProgressTracker:
    """Tracks and reports structured progress for model loading and generation"""
    
    def __init__(self):
        self.current_stage = "initializing"
        self.stage_progress = 0.0
        self.overall_progress = 0.0
        self.start_time = time.time()
        
    def emit_progress(self, stage: str, step: int, total: int, message: str = "", stage_weight: float = 1.0, preview_image: Optional[str] = None):
        """Emit structured progress update with optional preview image"""
        self.current_stage = stage
        self.stage_progress = (step / total) * 100 if total > 0 else 0
        
        # Calculate overall progress based on stage weights
        stage_weights = {
            "initializing": 0.05,
            "loading_model": 0.40,
            "generating": 0.50,
            "saving": 0.05
        }
        
        base_progress = sum(stage_weights[s] for s in stage_weights.keys() if s != stage and list(stage_weights.keys()).index(s) < list(stage_weights.keys()).index(stage))
        current_stage_contribution = (self.stage_progress / 100) * stage_weights.get(stage, 0.25)
        self.overall_progress = (base_progress + current_stage_contribution) * 100
        
        progress_data = {
            "stage": stage,
            "step": step,
            "total": total,
            "stage_progress": round(self.stage_progress, 1),
            "overall_progress": round(self.overall_progress, 1),
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "elapsed_time": round(time.time() - self.start_time, 2)
        }
        
        # Add preview image if provided
        if preview_image:
            progress_data["preview_image"] = preview_image
        
        # Output structured progress for pipeline manager to parse
        print(f"PROGRESS:{json.dumps(progress_data)}", flush=True)

# Global progress tracker
progress_tracker = ProgressTracker()

class ImageGenerator:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32
        # Application-level path resolution
        self.app_root = Path(__file__).parent.parent.parent
        self.models_base_path = self.app_root / "models" / "ImageGeneration"
        self.pipeline = None
        self.current_model = None
        
        print(f"ImageGenerator initialized:")
        print(f"  Device: {self.device}")
        print(f"  Torch dtype: {self.torch_dtype}")
        print(f"  App root: {self.app_root}")
        print(f"  Models path: {self.models_base_path}")
        
    def latents_to_preview_image(self, latents) -> Optional[str]:
        """Convert latents to base64 encoded preview image"""
        try:
            if self.pipeline is None or not hasattr(self.pipeline, 'vae'):
                return None

            # Decode latents to image using the VAE
            with torch.no_grad():
                # Make a copy of latents to avoid modifying the original
                latents_copy = latents.clone()

                # For SDXL, we need to handle the latents differently
                # Don't apply scaling factor here - let the VAE handle it
                print(f"Latents shape: {latents_copy.shape}, dtype: {latents_copy.dtype}")
                print(f"Latents min: {latents_copy.min()}, max: {latents_copy.max()}")

                # Decode latents to image tensor
                image_tensor = self.pipeline.vae.decode(latents_copy, return_dict=False)[0]
                print(f"Decoded tensor shape: {image_tensor.shape}, dtype: {image_tensor.dtype}")
                print(f"Decoded tensor min: {image_tensor.min()}, max: {image_tensor.max()}")

                # Convert from tensor to PIL Image with better handling
                # Normalize to [0, 1] range - VAE output is typically in [-1, 1]
                image_tensor = (image_tensor / 2 + 0.5).clamp(0, 1)
                print(f"After normalization min: {image_tensor.min()}, max: {image_tensor.max()}")

                # Move to CPU and convert to numpy with proper handling
                image_tensor = image_tensor.cpu().permute(0, 2, 3, 1).float().numpy()

                # Convert to PIL and resize for preview
                if image_tensor.shape[0] > 0:
                    # Ensure values are in valid range and handle NaN/inf
                    image_array = image_tensor[0]
                    print(f"Image array shape: {image_array.shape}, min: {image_array.min()}, max: {image_array.max()}")

                    image_array = np.nan_to_num(image_array, nan=0.0, posinf=1.0, neginf=0.0)
                    image_array = np.clip(image_array, 0.0, 1.0)
                    image_array = (image_array * 255).astype(np.uint8)

                    print(f"Final array min: {image_array.min()}, max: {image_array.max()}")

                    pil_image = Image.fromarray(image_array)

                    # Resize to larger preview for better visibility (512px max)
                    pil_image.thumbnail((512, 512), Image.Resampling.LANCZOS)

                    # Convert to base64
                    buffer = io.BytesIO()
                    pil_image.save(buffer, format='JPEG', quality=85)
                    buffer.seek(0)
                    base64_string = base64.b64encode(buffer.getvalue()).decode('utf-8')

                    # Return just the base64 string (frontend will add data URL prefix)
                    return base64_string

        except Exception as e:
            print(f"Warning: Failed to generate preview image: {e}")
            import traceback
            traceback.print_exc()

        return None
        
    def get_scheduler(self, scheduler_name: str, pipeline_config):
        """Get scheduler based on name and model type"""
        if self.current_model in ["flux-dev", "flux-dev-quantized"]:
            # ONLY these exact schedulers are allowed for FLUX
            flux_schedulers = [
                "sgm_uniform",
                "simple",
                "beta", 
                "ddim_uniform",
                "normal",
                "ddm_uniform",
                "sgm_discrete"
            ]
            # Return the scheduler name if valid, otherwise default to "normal"
            return scheduler_name if scheduler_name in flux_schedulers else "normal"
            
        # Standard diffusers schedulers for other models
        schedulers = {
            "euler": EulerDiscreteScheduler.from_config(pipeline_config),
            "euler_a": EulerAncestralDiscreteScheduler.from_config(pipeline_config), 
            "dpm": DPMSolverMultistepScheduler.from_config(pipeline_config),
            "lms": LMSDiscreteScheduler.from_config(pipeline_config),
            "pndm": PNDMScheduler.from_config(pipeline_config),
            "ddim": DDIMScheduler.from_config(pipeline_config)
        }
        return schedulers.get(scheduler_name, EulerAncestralDiscreteScheduler.from_config(pipeline_config))

    def load_model(self, model_name: str) -> bool:
        """Load the specified model"""
        try:
            if self.current_model == model_name and self.pipeline is not None:
                print(f"Model {model_name} already loaded")
                progress_tracker.emit_progress("loading_model", 7, 7, f"Model {model_name} already loaded")
                return True
                
            print(f"Loading model: {model_name}")
            progress_tracker.emit_progress("loading_model", 0, 7, f"Starting to load {model_name}")
            
            # Clear any existing pipeline
            if self.pipeline is not None:
                del self.pipeline
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            self.pipeline = None
            self.current_model = None
            progress_tracker.emit_progress("loading_model", 1, 7, "Cleared previous model")
            
            if model_name == "flux-dev-quantized":
                quant_model_path = self.models_base_path / "flux-dev-4bit"
                if not quant_model_path.exists():
                    print(f"Error: FLUX Dev 4-bit Quantized model not found at {quant_model_path}")
                    return False
                print("Loading FLUX Dev 4-bit Quantized pipeline...")
                progress_tracker.emit_progress("loading_model", 2, 7, "Loading quantized FLUX Dev components...")
                try:
                    self.pipeline = create_enhanced_quantized_flux_pipeline(
                        model_id=str(quant_model_path),
                        quant_model_id=str(quant_model_path),
                        torch_dtype=self.torch_dtype,
                        device=self.device
                    )
                    self.pipeline = optimize_pipeline_memory(self.pipeline)
                    self.pipeline = create_flux_with_callback_support(self.pipeline, self.device)
                    print(f"Memory usage after loading: {get_memory_usage()}")
                except Exception as e:
                    print(f"Failed to load quantized FLUX: {e}")
                    return False
                progress_tracker.emit_progress("loading_model", 5, 7, "FLUX Dev 4-bit Quantized pipeline loaded successfully")

            elif model_name == "flux-dev":
                regular_model_path = self.models_base_path / "fluxDev"
                if not regular_model_path.exists():
                    print(f"Error: FLUX Dev model not found at {regular_model_path}")
                    return False
                print("Loading regular FLUX Dev pipeline...")
                progress_tracker.emit_progress("loading_model", 2, 7, "Loading FLUX Dev components...")
                
                # Create base pipeline
                base_pipeline = FluxPipeline.from_pretrained(
                    str(regular_model_path),
                    torch_dtype=self.torch_dtype,
                    low_cpu_mem_usage=True
                )
                
                # Move to device first
                if torch.cuda.is_available():
                    base_pipeline = base_pipeline.to(self.device)
                    base_pipeline.enable_model_cpu_offload()
                
                # Wrap with callback support
                self.pipeline = create_flux_with_callback_support(base_pipeline, self.device)
                print("Successfully wrapped FLUX Dev pipeline with callback support")
                progress_tracker.emit_progress("loading_model", 5, 7, "FLUX Dev pipeline loaded successfully")

            elif model_name == "sdxl-turbo":
                model_path = self.models_base_path / "sdxl-turbo"
                if not model_path.exists():
                    print(f"Error: SDXL Turbo model not found at {model_path}")
                    return False
                    
                self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    str(model_path),
                    torch_dtype=self.torch_dtype,
                    low_cpu_mem_usage=True
                )
                if torch.cuda.is_available():
                    self.pipeline = self.pipeline.to(self.device)
                    self.pipeline.enable_model_cpu_offload()
                    
            elif model_name == "stable-diffusion-xl-base-1.0":
                model_path = self.models_base_path / "stable-diffusion-xl-base-1.0" 
                if not model_path.exists():
                    print(f"Error: SDXL Base model not found at {model_path}")
                    return False
                    
                progress_tracker.emit_progress("loading_model", 2, 7, "Loading SDXL Base components...")
                self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    str(model_path),
                    torch_dtype=self.torch_dtype,
                    low_cpu_mem_usage=True
                )
                progress_tracker.emit_progress("loading_model", 5, 7, "Moving SDXL to device...")
                if torch.cuda.is_available():
                    self.pipeline = self.pipeline.to(self.device)
                    self.pipeline.enable_model_cpu_offload()
                    
            elif model_name == "stable-diffusion-xl-refiner-1.0":
                model_path = self.models_base_path / "stable-diffusion-xl-refiner-1.0"
                if not model_path.exists():
                    print(f"Error: SDXL Refiner model not found at {model_path}")
                    return False
                    
                self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    str(model_path),
                    torch_dtype=self.torch_dtype,
                    low_cpu_mem_usage=True
                )
                if torch.cuda.is_available():
                    self.pipeline = self.pipeline.to(self.device)
                    self.pipeline.enable_model_cpu_offload()
                    
            elif model_name == "stable-diffusion-v1-5":
                model_path = self.models_base_path / "stable-diffusion-v1-5"
                if not model_path.exists():
                    print(f"Error: SD v1.5 model not found at {model_path}")
                    return False
                    
                self.pipeline = StableDiffusionPipeline.from_pretrained(
                    str(model_path),
                    torch_dtype=self.torch_dtype,
                    low_cpu_mem_usage=True
                )
                if torch.cuda.is_available():
                    self.pipeline = self.pipeline.to(self.device)
                    self.pipeline.enable_model_cpu_offload()
                    
            elif model_name == "stable-diffusion-2-1":
                model_path = self.models_base_path / "stable-diffusion-2-1"
                if not model_path.exists():
                    print(f"Error: SD v2.1 model not found at {model_path}")
                    return False
                    
                self.pipeline = StableDiffusionPipeline.from_pretrained(
                    str(model_path),
                    torch_dtype=self.torch_dtype,
                    low_cpu_mem_usage=True
                )
                if torch.cuda.is_available():
                    self.pipeline = self.pipeline.to(self.device)
                    self.pipeline.enable_model_cpu_offload()
                    
            else:
                print(f"Error: Unknown model '{model_name}'")
                return False
                
            progress_tracker.emit_progress("loading_model", 6, 7, "Finalizing model setup...")
            self.current_model = model_name
            progress_tracker.emit_progress("loading_model", 7, 7, f"Successfully loaded {model_name}")
            print(f"Successfully loaded {model_name}")
            return True
            
        except Exception as e:
            print(f"Error loading model {model_name}: {str(e)}")
            progress_tracker.emit_progress("loading_model", 0, 7, f"Failed to load {model_name}: {str(e)}")
            traceback.print_exc()
            return False

    def generate_image(self, prompt: str, **kwargs) -> Tuple[Optional[Image.Image], Dict[str, Any]]:
        """Generate image with the loaded model"""
        try:
            if self.pipeline is None:
                return None, {"error": "No model loaded"}
                
            # Extract parameters
            negative_prompt = kwargs.get('negative_prompt', '')
            width = kwargs.get('width', 1024)
            height = kwargs.get('height', 1024) 
            num_inference_steps = kwargs.get('steps', 20)
            guidance_scale = kwargs.get('guidance_scale', 7.5)
            seed = kwargs.get('seed')
            scheduler = kwargs.get('scheduler', 'euler_a')
            
            # SDXL Refiner parameters
            use_refiner = kwargs.get('use_refiner', False)
            refiner_steps = kwargs.get('refiner_steps', 10)
            
            # Set up generator for reproducibility
            generator = None
            if seed is not None:
                generator = torch.Generator(device=self.device).manual_seed(int(seed))
                
            # Set scheduler if specified and not default
            if scheduler != 'default':
                try:
                    # Handle different pipeline types (regular vs quantized)
                    if hasattr(self.pipeline, 'scheduler'):
                        self.pipeline.scheduler = self.get_scheduler(scheduler, self.pipeline.scheduler.config)
                    elif hasattr(self.pipeline, 'base_pipeline') and hasattr(self.pipeline.base_pipeline, 'scheduler'):
                        # For wrapped quantized pipeline
                        self.pipeline.base_pipeline.scheduler = self.get_scheduler(scheduler, self.pipeline.base_pipeline.scheduler.config)
                except Exception as e:
                    print(f"Warning: Could not set scheduler {scheduler}, using default: {e}")
            
            print(f"Generating image with prompt: '{prompt[:100]}{'...' if len(prompt) > 100 else ''}'")
            print(f"Parameters: {width}x{height}, steps={num_inference_steps}, guidance={guidance_scale}, seed={seed}")
            
            progress_tracker.emit_progress("generating", 0, num_inference_steps, f"Starting generation with {num_inference_steps} steps")
            
            # Create progress callback for denoising steps with preview images (for models that support it)
            def progress_callback(step, timestep, latents):
                preview_image = self.latents_to_preview_image(latents)
                progress_tracker.emit_progress("generating", step + 1, num_inference_steps, 
                                            f"Denoising step {step + 1}/{num_inference_steps}", 
                                            preview_image=preview_image)
            
            # Model-specific generation logic
            if self.current_model in ["flux-dev", "flux-dev-quantized"]:
                progress_tracker.emit_progress("generating", 1, num_inference_steps, "Starting FLUX Dev generation...")

                # Get FLUX-compatible scheduler name (for logging, not for passing)
                scheduler_name = self.get_scheduler(scheduler, None)
                print(f"Using FLUX scheduler: {scheduler_name}")

                # FLUX models don't support callback, so we'll simulate progress updates
                def flux_progress_callback(step, timestep, latents):
                    # Generate a simple preview from latents for FLUX
                    preview_image = self.latents_to_preview_image(latents)
                    progress_tracker.emit_progress("generating", step + 1, num_inference_steps,
                                                f"FLUX denoising step {step + 1}/{num_inference_steps}",
                                                preview_image=preview_image)

                # Try to use callback if supported, otherwise generate without
                try:
                    image = self.pipeline(
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        width=width,
                        height=height,
                        num_inference_steps=num_inference_steps,
                        guidance_scale=guidance_scale,
                        generator=generator,
                        max_sequence_length=512,
                        callback=flux_progress_callback,
                        callback_steps=1
                    ).images[0]
                except TypeError:
                    # Fallback if callback is not supported
                    print("FLUX callback not supported, generating without live preview")
                    image = self.pipeline(
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        width=width,
                        height=height,
                        num_inference_steps=num_inference_steps,
                        guidance_scale=guidance_scale,
                        generator=generator,
                        max_sequence_length=512
                    ).images[0]

                progress_tracker.emit_progress("generating", num_inference_steps, num_inference_steps, "FLUX Dev generation complete!")
                
            elif self.current_model == "sdxl-turbo":
                # SDXL Turbo is optimized for speed with fewer steps and low guidance
                steps = min(num_inference_steps, 4)  # SDXL Turbo works best with 1-4 steps
                guidance = min(guidance_scale, 2.0)  # Low guidance for SDXL Turbo
                image = self.pipeline(
                    prompt=prompt,
                    width=width,
                    height=height,
                    num_inference_steps=steps,
                    guidance_scale=guidance,
                    generator=generator,
                    callback=progress_callback,
                    callback_steps=1,
                ).images[0]
                
            elif self.current_model == "stable-diffusion-xl-base-1.0":
                # SDXL Base model with optional refiner
                if use_refiner and refiner_steps > 0:
                    # --- SDXL Two-Stage Workflow: Base + Refiner ---
                    progress_tracker.emit_progress("generating", 1, num_inference_steps, "Generating with SDXL Base model...")
                    def base_progress_callback(step, timestep, latents):
                        preview_image = self.latents_to_preview_image(latents)
                        progress_tracker.emit_progress("generating", step + 1, num_inference_steps, 
                                                    f"Base denoising step {step + 1}/{num_inference_steps}", 
                                                    preview_image=preview_image)
                    # Run base pipeline, output as PIL for refiner
                    base_result = self.pipeline(
                        prompt=prompt,
                        negative_prompt=negative_prompt if negative_prompt else None,
                        width=width,
                        height=height,
                        num_inference_steps=num_inference_steps,
                        guidance_scale=guidance_scale,
                        generator=generator,
                        callback=base_progress_callback,
                        callback_steps=1,
                        output_type="pil",
                        return_dict=True,
                    )
                    base_image = base_result.images[0]
                    progress_tracker.emit_progress("generating", num_inference_steps, num_inference_steps, "Base generation complete. Running SDXL Refiner...")

                    # --- Load SDXL Refiner pipeline ---
                    refiner_model_path = self.models_base_path / "stable-diffusion-xl-refiner-1.0"
                    if not refiner_model_path.exists():
                        print(f"Error: SDXL Refiner model not found at {refiner_model_path}")
                        return None, {"error": "SDXL Refiner model not found"}
                    refiner_pipeline = StableDiffusionXLImg2ImgPipeline.from_pretrained(
                        str(refiner_model_path),
                        torch_dtype=self.torch_dtype,
                        low_cpu_mem_usage=True
                    )
                    if torch.cuda.is_available():
                        refiner_pipeline = refiner_pipeline.to(self.device)
                        refiner_pipeline.enable_model_cpu_offload()

                    # --- Run refiner pipeline ---
                    # Always use the user-specified refiner_steps for num_inference_steps
                    def refiner_progress_callback(step, timestep, latents):
                        preview_image = self.latents_to_preview_image(latents)
                        progress_tracker.emit_progress("generating", step + 1, refiner_steps, 
                            f"Refiner denoising step {step + 1}/{refiner_steps}", preview_image=preview_image)
                    refiner_result = refiner_pipeline(
                        prompt=prompt,
                        negative_prompt=negative_prompt if negative_prompt else None,
                        image=base_image,
                        num_inference_steps=refiner_steps,  # Enforce user setting
                        strength=1.0,  # Run full denoising steps (see Diffusers SDXL Img2Img docs)
                        guidance_scale=guidance_scale,
                        generator=generator,
                        callback=refiner_progress_callback,
                        callback_steps=1,
                        output_type="pil",
                        return_dict=True,
                    )
                    image = refiner_result.images[0]
                    progress_tracker.emit_progress("generating", refiner_steps, refiner_steps, "SDXL Refiner complete!")
                else:
                    # Normal SDXL Base generation without refiner
                    def progress_callback(step, timestep, latents):
                        preview_image = self.latents_to_preview_image(latents)
                        progress_tracker.emit_progress("generating", step + 1, num_inference_steps, 
                                                    f"Denoising step {step + 1}/{num_inference_steps}", 
                                                    preview_image=preview_image)
                    
                    # Generate image
                    result = self.pipeline(
                        prompt=prompt,
                        negative_prompt=negative_prompt if negative_prompt else None,
                        width=width,
                        height=height,
                        num_inference_steps=num_inference_steps,
                        guidance_scale=guidance_scale,
                        generator=generator,
                        callback=progress_callback,
                        callback_steps=1,
                    )
                    image = result.images[0]
                
            else:
                # Standard Stable Diffusion models (v1.5, v2.1)
                image = self.pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt if negative_prompt else None,
                    width=width,
                    height=height,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    generator=generator,
                    callback=progress_callback,
                    callback_steps=1,
                ).images[0]
            
            return image, {"success": True}
            
        except Exception as e:
            error_msg = f"Error during image generation: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None, {"error": error_msg}

def main():
    parser = argparse.ArgumentParser(description="Generate images using various AI models")
    parser.add_argument("--prompt", required=True, help="Text prompt for image generation")
    parser.add_argument("--output", required=True, help="Output image path")
    parser.add_argument("--model", default="sdxl-turbo", help="Model to use for generation")
    parser.add_argument("--width", type=int, default=1024, help="Image width")
    parser.add_argument("--height", type=int, default=1024, help="Image height") 
    parser.add_argument("--steps", type=int, default=20, help="Number of inference steps")
    parser.add_argument("--guidance_scale", type=float, default=7.5, help="Guidance scale")
    parser.add_argument("--seed", type=int, help="Random seed for reproducibility")
    parser.add_argument("--negative_prompt", default="", help="Negative prompt")
    parser.add_argument("--scheduler", default="euler_a", help="Scheduler to use")
    parser.add_argument("--use_refiner", action="store_true", help="Use SDXL Refiner for enhancement (SDXL Base only)")
    parser.add_argument("--refiner_steps", type=int, default=10, help="Number of refiner steps (5-20 recommended)")
    parser.add_argument("--result_file", help="JSON file to write results")
    
    args = parser.parse_args()
    
    start_time = time.time()
    
    try:
        # Initialize generator
        progress_tracker.emit_progress("initializing", 1, 2, "Initializing image generator...")
        generator = ImageGenerator()
        progress_tracker.emit_progress("initializing", 2, 2, "Generator initialized")
        
        # Load model
        print(f"Initializing {args.model} model...")
        if not generator.load_model(args.model):
            result = {
                "success": False,
                "error": f"Failed to load model {args.model}",
                "execution_time": time.time() - start_time
            }
            if args.result_file:
                with open(args.result_file, 'w') as f:
                    json.dump(result, f, indent=2)
            print(json.dumps(result))
            sys.exit(1)
        
        # Generate image  
        print("Starting image generation...")
        image, generation_result = generator.generate_image(
            prompt=args.prompt,
            negative_prompt=args.negative_prompt,
            width=args.width,
            height=args.height,
            steps=args.steps,
            guidance_scale=args.guidance_scale,
            seed=args.seed,
            scheduler=args.scheduler,
            use_refiner=args.use_refiner,
            refiner_steps=args.refiner_steps
        )
        
        if image is None or not generation_result.get("success", False):
            result = {
                "success": False,
                "error": generation_result.get("error", "Unknown generation error"),
                "execution_time": time.time() - start_time
            }
            if args.result_file:
                with open(args.result_file, 'w') as f:
                    json.dump(result, f, indent=2)
            print(json.dumps(result))
            sys.exit(1)
            
        # Save image
        progress_tracker.emit_progress("saving", 1, 1, f"Saving image to {args.output}")
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        image.save(args.output)
        progress_tracker.emit_progress("saving", 1, 1, f"Image saved successfully! Total time: {time.time() - start_time:.1f}s")
        
        # Prepare result
        result = {
            "success": True,
            "output_path": args.output,
            "model": args.model,
            "prompt": args.prompt,
            "width": args.width,
            "height": args.height,
            "steps": args.steps,
            "guidance_scale": args.guidance_scale,
            "seed": args.seed,
            "execution_time": time.time() - start_time
        }
        
        # Write result file if specified
        if args.result_file:
            with open(args.result_file, 'w') as f:
                json.dump(result, f, indent=2)
        
        print(f"Successfully generated image: {args.output}")
        print(json.dumps(result))
        
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        
        result = {
            "success": False,
            "error": error_msg,
            "execution_time": time.time() - start_time
        }
        
        if args.result_file:
            try:
                with open(args.result_file, 'w') as f:
                    json.dump(result, f, indent=2)
            except:
                pass
                
        print(json.dumps(result))
        sys.exit(1)

if __name__ == "__main__":
    main()
