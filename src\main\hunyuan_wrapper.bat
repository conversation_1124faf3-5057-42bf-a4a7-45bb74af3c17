@echo off
rem Wrapper script to launch Hunyuan server with custom port
rem This avoids issues with spaces in paths when passing parameters

set HUNYUAN_DIR=%~dp0..\..\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)
set HUNYUAN_BAT=%HUNYUAN_DIR%\run-stableprojectorz-full-multiview.bat
set HUNYUAN_PORT=%1

if "%HUNYUAN_PORT%"=="" set HUNYUAN_PORT=7961

echo Starting Hunyuan server on port %HUNYUAN_PORT%...
cd /d "%HUNYUAN_DIR%"
call "%HUNYUAN_BAT%" --port %HUNYUAN_PORT%
